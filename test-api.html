<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>Laravel API Test</h1>
    <button onclick="testAPI()">Test API Connection</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            const API_BASE_URL = 'http://127.0.0.1:8000/api/v1';
            
            resultDiv.innerHTML = '<div class="result loading">Testing API connection...</div>';
            
            try {
                console.log('Fetching from:', `${API_BASE_URL}/products`);
                
                const response = await fetch(`${API_BASE_URL}/products`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors',
                });
                
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}\nResponse: ${errorText}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data);
                
                if (data.success && Array.isArray(data.data)) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ API Connection Successful!</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <p><strong>Products Found:</strong> ${data.data.length}</p>
                            <p><strong>Response:</strong></p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error('API returned unsuccessful response or invalid data format');
                }
            } catch (err) {
                console.error('Error testing API:', err);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ API Connection Failed</h3>
                        <p><strong>Error:</strong> ${err.message}</p>
                        <p><strong>API URL:</strong> ${API_BASE_URL}/products</p>
                        <p><strong>Suggestion:</strong> Make sure Laravel server is running on port 8000</p>
                    </div>
                `;
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
