<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reminders', function (Blueprint $table) {
            $table->id();

            // User identification (nullable for guest users)
            $table->unsignedBigInteger('user_id')->nullable();

            // Reminder content
            $table->string('title');
            $table->text('description')->nullable();

            // Reminder timing
            $table->date('reminder_date');
            $table->time('reminder_time');

            // Status tracking
            $table->enum('status', ['pending', 'completed', 'cancelled'])->default('pending');

            // Priority level (1-5, where 5 is highest priority)
            $table->integer('priority')->default(3);

            // Additional metadata
            $table->json('metadata')->nullable(); // For future extensibility

            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index(['reminder_date', 'reminder_time']);
            $table->index(['status', 'priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reminders');
    }
};
