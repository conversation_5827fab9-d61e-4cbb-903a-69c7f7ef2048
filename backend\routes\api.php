<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\InformationArticleController;
use App\Http\Controllers\ReminderController;

// Authentication routes would go here if needed

// API routes for React PWA
Route::prefix('v1')->group(function () {
    // Products API (read-only)
    Route::get('/products', [ProductController::class, 'apiIndex']);
    Route::get('/products/{product}', [ProductController::class, 'apiShow']);

    // Information Articles API (read-only)
    Route::get('/information', [InformationArticleController::class, 'apiIndex']);
    Route::get('/information/{informationArticle:slug}', [InformationArticleController::class, 'apiShow']);
    Route::get('/information-categories', [InformationArticleController::class, 'apiCategories']);
    Route::post('/information/{informationArticle:slug}/like', [InformationArticleController::class, 'apiLike']);

    // Reminders API (full CRUD)
    Route::get('/reminders', [ReminderController::class, 'apiIndex']);
    Route::post('/reminders', [ReminderController::class, 'apiStore']);
    Route::get('/reminders/{reminder}', [ReminderController::class, 'apiShow']);
    Route::put('/reminders/{reminder}', [ReminderController::class, 'apiUpdate']);
    Route::patch('/reminders/{reminder}', [ReminderController::class, 'apiUpdate']);
    Route::delete('/reminders/{reminder}', [ReminderController::class, 'apiDestroy']);

    // Reminder status actions
    Route::post('/reminders/{reminder}/complete', [ReminderController::class, 'apiMarkCompleted']);
    Route::post('/reminders/{reminder}/cancel', [ReminderController::class, 'apiMarkCancelled']);

    // Reminder statistics
    Route::get('/reminders-stats', [ReminderController::class, 'apiStats']);
});
