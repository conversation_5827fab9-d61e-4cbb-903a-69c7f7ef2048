@extends('layouts.app')

@section('title', $informationArticle->title)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-newspaper"></i> Detail Artikel
    </h1>
    <div>
        <a href="{{ route('information_articles.edit', $informationArticle) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit"></i> Edit
        </a>
        <a href="{{ route('information_articles.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <!-- Article Header -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h1 class="h2 mb-2">{{ $informationArticle->title }}</h1>
                            @if($informationArticle->excerpt)
                                <p class="lead text-muted">{{ $informationArticle->excerpt }}</p>
                            @endif
                        </div>
                        <div class="text-end">
                            @switch($informationArticle->status)
                                @case('published')
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check"></i> Published
                                    </span>
                                    @break
                                @case('draft')
                                    <span class="badge bg-warning fs-6">
                                        <i class="fas fa-edit"></i> Draft
                                    </span>
                                    @break
                                @case('archived')
                                    <span class="badge bg-dark fs-6">
                                        <i class="fas fa-archive"></i> Archived
                                    </span>
                                    @break
                            @endswitch
                            @if($informationArticle->is_featured)
                                <span class="badge bg-warning fs-6 ms-1">
                                    <i class="fas fa-star"></i> Featured
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Article Meta -->
                    <div class="d-flex flex-wrap gap-3 text-muted small mb-3">
                        @if($informationArticle->author)
                            <span><i class="fas fa-user"></i> {{ $informationArticle->author }}</span>
                        @endif
                        @if($informationArticle->published_at)
                            <span><i class="fas fa-calendar"></i> {{ $informationArticle->published_at->format('d M Y H:i') }}</span>
                        @endif
                        <span><i class="fas fa-folder"></i> {{ $informationArticle->category }}</span>
                        <span><i class="fas fa-sort-numeric-up"></i> Prioritas: {{ $informationArticle->priority }}</span>
                        @if($informationArticle->reading_time)
                            <span><i class="fas fa-clock"></i> ~{{ $informationArticle->reading_time }} menit baca</span>
                        @endif
                    </div>

                    <!-- Tags -->
                    @if($informationArticle->tags && count($informationArticle->tags) > 0)
                        <div class="mb-3">
                            @foreach($informationArticle->tags as $tag)
                                <span class="badge bg-light text-dark me-1">#{{ $tag }}</span>
                            @endforeach
                        </div>
                    @endif
                </div>

                <!-- Featured Image -->
                @if($informationArticle->featured_image)
                    <div class="mb-4">
                        <img src="{{ $informationArticle->featured_image }}" 
                             alt="{{ $informationArticle->image_alt_text ?: $informationArticle->title }}" 
                             class="img-fluid rounded">
                        @if($informationArticle->image_alt_text)
                            <small class="text-muted d-block mt-1">{{ $informationArticle->image_alt_text }}</small>
                        @endif
                    </div>
                @endif

                <!-- Article Content -->
                <div class="article-content">
                    {!! nl2br(e($informationArticle->content)) !!}
                </div>

                <!-- Article Footer -->
                <hr class="my-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted small">
                        <div>Dibuat: {{ $informationArticle->created_at->format('d M Y H:i') }}</div>
                        <div>Diperbarui: {{ $informationArticle->updated_at->format('d M Y H:i') }}</div>
                    </div>
                    <div class="d-flex gap-3">
                        <span class="badge bg-info">
                            <i class="fas fa-eye"></i> {{ number_format($informationArticle->view_count) }} views
                        </span>
                        <span class="badge bg-danger">
                            <i class="fas fa-heart"></i> {{ number_format($informationArticle->like_count) }} likes
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">Aksi Cepat</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('information_articles.edit', $informationArticle) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Artikel
                    </a>
                    @if($informationArticle->status === 'published')
                        <button class="btn btn-outline-info" onclick="copyToClipboard('{{ url('/api/v1/information/' . $informationArticle->slug) }}')">
                            <i class="fas fa-link"></i> Copy API URL
                        </button>
                    @endif
                    <form action="{{ route('information_articles.destroy', $informationArticle) }}" 
                          method="POST" class="d-inline"
                          onsubmit="return confirm('Apakah Anda yakin ingin menghapus artikel ini?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash"></i> Hapus Artikel
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Article Info -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">Informasi Artikel</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td>{{ $informationArticle->id }}</td>
                    </tr>
                    <tr>
                        <td><strong>Slug:</strong></td>
                        <td><code>{{ $informationArticle->slug }}</code></td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            @switch($informationArticle->status)
                                @case('published')
                                    <span class="badge bg-success">Published</span>
                                    @break
                                @case('draft')
                                    <span class="badge bg-warning">Draft</span>
                                    @break
                                @case('archived')
                                    <span class="badge bg-dark">Archived</span>
                                    @break
                            @endswitch
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Kategori:</strong></td>
                        <td><span class="badge bg-secondary">{{ $informationArticle->category }}</span></td>
                    </tr>
                    <tr>
                        <td><strong>Prioritas:</strong></td>
                        <td><span class="badge bg-info">{{ $informationArticle->priority }}</span></td>
                    </tr>
                    <tr>
                        <td><strong>Featured:</strong></td>
                        <td>
                            @if($informationArticle->is_featured)
                                <span class="badge bg-warning"><i class="fas fa-star"></i> Ya</span>
                            @else
                                <span class="text-muted">Tidak</span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- SEO Info -->
        @if($informationArticle->meta_title || $informationArticle->meta_description)
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">SEO Information</h6>
                </div>
                <div class="card-body">
                    @if($informationArticle->meta_title)
                        <div class="mb-2">
                            <strong>Meta Title:</strong>
                            <div class="text-muted small">{{ $informationArticle->meta_title }}</div>
                        </div>
                    @endif
                    @if($informationArticle->meta_description)
                        <div>
                            <strong>Meta Description:</strong>
                            <div class="text-muted small">{{ $informationArticle->meta_description }}</div>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('URL berhasil disalin ke clipboard!');
    }, function(err) {
        console.error('Gagal menyalin URL: ', err);
    });
}
</script>

<style>
.article-content {
    font-size: 1.1rem;
    line-height: 1.7;
}

.article-content p {
    margin-bottom: 1rem;
}
</style>
@endsection
