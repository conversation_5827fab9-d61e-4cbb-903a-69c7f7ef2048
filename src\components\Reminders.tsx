import React, { useState, useEffect } from 'react';
import type { <PERSON>mind<PERSON>, RemindersProps, ReminderStats } from '../types';
import UniversalHeader from './UniversalHeader';
import HomeButton from './HomeButton';
import { reminderApi } from '../utils/reminderApi';
import { storage } from '../utils/storage';

/**
 * Komponen Reminders - Sistem pengingat dengan CRUD lengkap
 * Menggunakan API Laravel backend dengan offline-first functionality
 * Design mengikuti pola PWA yang sudah ada dengan Tailwind CSS + DaisyUI
 */
const Reminders: React.FC<RemindersProps> = ({ className = '' }) => {
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [selectedReminder, setSelectedReminder] = useState<Reminder | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<ReminderStats | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showForm, setShowForm] = useState(false);
  const [editingReminder, setEditingReminder] = useState<Reminder | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    reminder_date: '',
    reminder_time: '',
    priority: 3,
    status: 'pending' as const
  });

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Load reminders on component mount
  useEffect(() => {
    loadReminders();
    loadStats();
  }, []);

  // Filter reminders when status or search changes
  useEffect(() => {
    loadReminders();
  }, [statusFilter, searchQuery, currentPage]);

  /**
   * Load reminders from API with caching
   */
  const loadReminders = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters = {
        page: currentPage,
        per_page: 10,
        ...(statusFilter && { status: statusFilter as any }),
        ...(searchQuery && { search: searchQuery })
      };

      let data;
      
      if (isOnline) {
        // Try to fetch from API
        const response = await reminderApi.getReminders(filters);
        data = response.data;
        
        // Cache the data
        await storage.setItem('reminders_cache', data);
        await storage.setItem('reminders_cache_timestamp', Date.now());
      } else {
        // Load from cache when offline
        const cachedData = await storage.getItem('reminders_cache');
        if (cachedData) {
          data = cachedData;
        } else {
          throw new Error('Tidak ada data tersimpan untuk mode offline');
        }
      }

      setReminders(data);
      
      // Auto-select first reminder if none selected
      if (data.length > 0 && !selectedReminder) {
        setSelectedReminder(data[0]);
      }
    } catch (err) {
      console.error('Error loading reminders:', err);
      setError(err instanceof Error ? err.message : 'Gagal memuat data reminder');
      
      // Try to load from cache as fallback
      try {
        const cachedData = await storage.getItem('reminders_cache');
        if (cachedData) {
          setReminders(cachedData);
          setError('Menampilkan data tersimpan (offline)');
        }
      } catch (cacheErr) {
        console.error('Cache fallback failed:', cacheErr);
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load reminder statistics
   */
  const loadStats = async () => {
    try {
      if (isOnline) {
        const response = await reminderApi.getStats();
        setStats(response.data);
        await storage.setItem('reminder_stats_cache', response.data);
      } else {
        const cachedStats = await storage.getItem('reminder_stats_cache');
        if (cachedStats) {
          setStats(cachedStats);
        }
      }
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  };

  /**
   * Handle form submission for create/update
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isOnline) {
      setError('Fitur ini memerlukan koneksi internet');
      return;
    }

    try {
      setLoading(true);
      
      if (editingReminder) {
        // Update existing reminder
        await reminderApi.updateReminder(editingReminder.id, formData);
      } else {
        // Create new reminder
        await reminderApi.createReminder(formData);
      }
      
      // Reset form and reload data
      resetForm();
      await loadReminders();
      await loadStats();
      
    } catch (err) {
      console.error('Error saving reminder:', err);
      setError(err instanceof Error ? err.message : 'Gagal menyimpan reminder');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle reminder deletion
   */
  const handleDelete = async (id: number) => {
    if (!isOnline) {
      setError('Fitur ini memerlukan koneksi internet');
      return;
    }

    if (!confirm('Apakah Anda yakin ingin menghapus reminder ini?')) {
      return;
    }

    try {
      setLoading(true);
      await reminderApi.deleteReminder(id);
      
      // Remove from local state
      setReminders(prev => prev.filter(r => r.id !== id));
      
      // Clear selection if deleted reminder was selected
      if (selectedReminder?.id === id) {
        setSelectedReminder(null);
      }
      
      await loadStats();
    } catch (err) {
      console.error('Error deleting reminder:', err);
      setError(err instanceof Error ? err.message : 'Gagal menghapus reminder');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle marking reminder as completed
   */
  const handleMarkCompleted = async (id: number) => {
    if (!isOnline) {
      setError('Fitur ini memerlukan koneksi internet');
      return;
    }

    try {
      setLoading(true);
      const response = await reminderApi.markCompleted(id);
      
      // Update local state
      setReminders(prev => prev.map(r => 
        r.id === id ? response.data : r
      ));
      
      // Update selected reminder if it's the one being updated
      if (selectedReminder?.id === id) {
        setSelectedReminder(response.data);
      }
      
      await loadStats();
    } catch (err) {
      console.error('Error marking reminder as completed:', err);
      setError(err instanceof Error ? err.message : 'Gagal menandai reminder selesai');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Reset form to initial state
   */
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      reminder_date: '',
      reminder_time: '',
      priority: 3,
      status: 'pending'
    });
    setEditingReminder(null);
    setShowForm(false);
  };

  /**
   * Start editing a reminder
   */
  const startEdit = (reminder: Reminder) => {
    setFormData({
      title: reminder.title,
      description: reminder.description || '',
      reminder_date: reminder.reminder_date,
      reminder_time: reminder.reminder_time,
      priority: reminder.priority,
      status: reminder.status
    });
    setEditingReminder(reminder);
    setShowForm(true);
  };

  /**
   * Get priority badge color
   */
  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 5: return 'badge-error';
      case 4: return 'badge-warning';
      case 3: return 'badge-info';
      case 2: return 'badge-success';
      case 1: return 'badge-ghost';
      default: return 'badge-info';
    }
  };

  /**
   * Get status badge color
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'badge-success';
      case 'cancelled': return 'badge-error';
      case 'pending': return 'badge-warning';
      default: return 'badge-ghost';
    }
  };

  /**
   * Format date for display
   */
  const formatDate = (date: string, time: string) => {
    try {
      const dateTime = new Date(`${date}T${time}`);
      return dateTime.toLocaleString('id-ID', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return `${date} ${time}`;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 ${className}`}>
      <UniversalHeader title="Reminder" />
      
      <div className="container mx-auto px-4 py-6">
        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="stat bg-white rounded-lg shadow-sm">
              <div className="stat-title text-xs">Total</div>
              <div className="stat-value text-lg text-primary">{stats.total}</div>
            </div>
            <div className="stat bg-white rounded-lg shadow-sm">
              <div className="stat-title text-xs">Menunggu</div>
              <div className="stat-value text-lg text-warning">{stats.pending}</div>
            </div>
            <div className="stat bg-white rounded-lg shadow-sm">
              <div className="stat-title text-xs">Selesai</div>
              <div className="stat-value text-lg text-success">{stats.completed}</div>
            </div>
            <div className="stat bg-white rounded-lg shadow-sm">
              <div className="stat-title text-xs">Terlambat</div>
              <div className="stat-value text-lg text-error">{stats.overdue}</div>
            </div>
            <div className="stat bg-white rounded-lg shadow-sm">
              <div className="stat-title text-xs">Hari Ini</div>
              <div className="stat-value text-lg text-info">{stats.upcoming_today}</div>
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Cari reminder..."
              className="input input-bordered w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <select
            className="select select-bordered"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="">Semua Status</option>
            <option value="pending">Menunggu</option>
            <option value="completed">Selesai</option>
            <option value="cancelled">Dibatalkan</option>
          </select>
          <button
            className="btn btn-primary"
            onClick={() => setShowForm(true)}
            disabled={!isOnline}
          >
            + Tambah Reminder
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="alert alert-error mb-4">
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              ✕
            </button>
          </div>
        )}

        {/* Offline Indicator */}
        {!isOnline && (
          <div className="alert alert-warning mb-4">
            <span>Mode Offline - Beberapa fitur terbatas</span>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-8">
            <span className="loading loading-spinner loading-lg"></span>
          </div>
        )}

        {/* Main Content */}
        {!loading && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Reminder List */}
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="text-lg font-semibold mb-4">Daftar Reminder</h3>

              {reminders.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>Belum ada reminder</p>
                  <button
                    className="btn btn-primary btn-sm mt-2"
                    onClick={() => setShowForm(true)}
                    disabled={!isOnline}
                  >
                    Buat Reminder Pertama
                  </button>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {reminders.map((reminder) => (
                    <div
                      key={reminder.id}
                      className={`card card-compact bg-base-100 border cursor-pointer transition-all hover:shadow-md ${
                        selectedReminder?.id === reminder.id ? 'border-primary shadow-md' : 'border-gray-200'
                      }`}
                      onClick={() => setSelectedReminder(reminder)}
                    >
                      <div className="card-body">
                        <div className="flex justify-between items-start">
                          <h4 className="card-title text-sm">{reminder.title}</h4>
                          <div className="flex gap-1">
                            <span className={`badge badge-xs ${getPriorityColor(reminder.priority)}`}>
                              P{reminder.priority}
                            </span>
                            <span className={`badge badge-xs ${getStatusColor(reminder.status)}`}>
                              {reminder.status === 'pending' ? 'Menunggu' :
                               reminder.status === 'completed' ? 'Selesai' : 'Dibatalkan'}
                            </span>
                          </div>
                        </div>

                        <p className="text-xs text-gray-600 line-clamp-2">
                          {reminder.description || 'Tidak ada deskripsi'}
                        </p>

                        <div className="flex justify-between items-center mt-2">
                          <span className="text-xs text-gray-500">
                            {formatDate(reminder.reminder_date, reminder.reminder_time)}
                          </span>

                          <div className="flex gap-1">
                            {reminder.status === 'pending' && (
                              <>
                                <button
                                  className="btn btn-xs btn-success"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleMarkCompleted(reminder.id);
                                  }}
                                  disabled={!isOnline}
                                >
                                  ✓
                                </button>
                                <button
                                  className="btn btn-xs btn-primary"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEdit(reminder);
                                  }}
                                  disabled={!isOnline}
                                >
                                  ✎
                                </button>
                              </>
                            )}
                            <button
                              className="btn btn-xs btn-error"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(reminder.id);
                              }}
                              disabled={!isOnline}
                            >
                              ✕
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Reminder Detail */}
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="text-lg font-semibold mb-4">Detail Reminder</h3>

              {selectedReminder ? (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-lg">{selectedReminder.title}</h4>
                    <div className="flex gap-2 mt-2">
                      <span className={`badge ${getPriorityColor(selectedReminder.priority)}`}>
                        Prioritas {selectedReminder.priority}
                      </span>
                      <span className={`badge ${getStatusColor(selectedReminder.status)}`}>
                        {selectedReminder.status === 'pending' ? 'Menunggu' :
                         selectedReminder.status === 'completed' ? 'Selesai' : 'Dibatalkan'}
                      </span>
                    </div>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-700">Deskripsi:</h5>
                    <p className="text-gray-600 mt-1">
                      {selectedReminder.description || 'Tidak ada deskripsi'}
                    </p>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-700">Waktu Pengingat:</h5>
                    <p className="text-gray-600 mt-1">
                      {formatDate(selectedReminder.reminder_date, selectedReminder.reminder_time)}
                    </p>
                  </div>

                  <div className="flex gap-2 pt-4">
                    {selectedReminder.status === 'pending' && (
                      <>
                        <button
                          className="btn btn-success btn-sm"
                          onClick={() => handleMarkCompleted(selectedReminder.id)}
                          disabled={!isOnline}
                        >
                          Tandai Selesai
                        </button>
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={() => startEdit(selectedReminder)}
                          disabled={!isOnline}
                        >
                          Edit
                        </button>
                      </>
                    )}
                    <button
                      className="btn btn-error btn-sm"
                      onClick={() => handleDelete(selectedReminder.id)}
                      disabled={!isOnline}
                    >
                      Hapus
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>Pilih reminder untuk melihat detail</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Form Modal */}
        {showForm && (
          <div className="modal modal-open">
            <div className="modal-box">
              <h3 className="font-bold text-lg mb-4">
                {editingReminder ? 'Edit Reminder' : 'Tambah Reminder Baru'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="label">
                    <span className="label-text">Judul *</span>
                  </label>
                  <input
                    type="text"
                    className="input input-bordered w-full"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                  />
                </div>

                <div>
                  <label className="label">
                    <span className="label-text">Deskripsi</span>
                  </label>
                  <textarea
                    className="textarea textarea-bordered w-full"
                    rows={3}
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="label">
                      <span className="label-text">Tanggal *</span>
                    </label>
                    <input
                      type="date"
                      className="input input-bordered w-full"
                      value={formData.reminder_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, reminder_date: e.target.value }))}
                      min={new Date().toISOString().split('T')[0]}
                      required
                    />
                  </div>

                  <div>
                    <label className="label">
                      <span className="label-text">Waktu *</span>
                    </label>
                    <input
                      type="time"
                      className="input input-bordered w-full"
                      value={formData.reminder_time}
                      onChange={(e) => setFormData(prev => ({ ...prev, reminder_time: e.target.value }))}
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="label">
                    <span className="label-text">Prioritas</span>
                  </label>
                  <select
                    className="select select-bordered w-full"
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                  >
                    <option value={1}>1 - Sangat Rendah</option>
                    <option value={2}>2 - Rendah</option>
                    <option value={3}>3 - Sedang</option>
                    <option value={4}>4 - Tinggi</option>
                    <option value={5}>5 - Sangat Tinggi</option>
                  </select>
                </div>

                {editingReminder && (
                  <div>
                    <label className="label">
                      <span className="label-text">Status</span>
                    </label>
                    <select
                      className="select select-bordered w-full"
                      value={formData.status}
                      onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                    >
                      <option value="pending">Menunggu</option>
                      <option value="completed">Selesai</option>
                      <option value="cancelled">Dibatalkan</option>
                    </select>
                  </div>
                )}

                <div className="modal-action">
                  <button
                    type="button"
                    className="btn btn-ghost"
                    onClick={resetForm}
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading || !isOnline}
                  >
                    {loading ? 'Menyimpan...' : (editingReminder ? 'Update' : 'Simpan')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>

      <HomeButton />
    </div>
  );
};

export default Reminders;
