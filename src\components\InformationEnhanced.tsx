import React, { useState, useEffect } from 'react';
import './Information.css';
import type { InformationArticle, InformationProps } from '../types';
import UniversalHeader from './UniversalHeader';
import HomeButton from './HomeButton';
import { informationApi } from '../utils/informationApi';
import { storage } from '../utils/storage';

/**
 * Komponen Information Enhanced - Menggunakan API Laravel backend
 * Layout split dengan daftar informasi di kiri dan detail di kanan
 * Fitur reordering: item yang diklik pindah ke posisi teratas
 * Offline-first dengan caching di IndexedDB
 */
const InformationEnhanced: React.FC<InformationProps> = ({ className = '' }) => {
  const [articles, setArticles] = useState<InformationArticle[]>([]);
  const [selectedArticle, setSelectedArticle] = useState<InformationArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Load articles on component mount
  useEffect(() => {
    loadArticles();
    loadCategories();
  }, []);

  // Filter articles when category or search changes
  useEffect(() => {
    loadArticles();
  }, [selectedCategory, searchQuery]);

  /**
   * Load articles from API or cache
   */
  const loadArticles = async () => {
    try {
      setLoading(true);
      setError(null);

      let articlesData: InformationArticle[] = [];

      if (isOnline) {
        try {
          // Try to fetch from API
          const response = await informationApi.getArticles({
            category: selectedCategory || undefined,
            search: searchQuery || undefined,
            per_page: 50
          });
          
          articlesData = response.data;
          
          // Cache the articles
          await storage.saveInformationArticles(articlesData);
          console.log('[InformationEnhanced] Articles cached successfully');
        } catch (apiError) {
          console.warn('[InformationEnhanced] API failed, falling back to cache:', apiError);
          // Fall back to cache
          articlesData = await storage.getInformationArticles();
        }
      } else {
        // Load from cache when offline
        articlesData = await storage.getInformationArticles();
        console.log('[InformationEnhanced] Loaded articles from cache (offline mode)');
      }

      // Apply client-side filtering if needed
      if (selectedCategory || searchQuery) {
        articlesData = articlesData.filter(article => {
          const matchesCategory = !selectedCategory || article.category === selectedCategory;
          const matchesSearch = !searchQuery || 
            article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            article.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (article.excerpt && article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()));
          
          return matchesCategory && matchesSearch;
        });
      }

      // Sort by priority (highest first), then by published date
      articlesData.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return new Date(b.published_at).getTime() - new Date(a.published_at).getTime();
      });

      setArticles(articlesData);

      if (articlesData.length === 0) {
        setError(isOnline ? 'Tidak ada artikel yang ditemukan' : 'Tidak ada artikel tersimpan offline');
      }
    } catch (error) {
      console.error('[InformationEnhanced] Error loading articles:', error);
      setError('Gagal memuat artikel informasi');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load categories from API or cache
   */
  const loadCategories = async () => {
    try {
      if (isOnline) {
        const response = await informationApi.getCategories();
        setCategories(response.data);
      }
    } catch (error) {
      console.warn('[InformationEnhanced] Failed to load categories:', error);
      // Use default categories
      setCategories([
        'Peringatan Nasional',
        'Promo & Diskon',
        'Prestasi & Penghargaan',
        'Tips & Panduan',
        'Berita Perusahaan'
      ]);
    }
  };

  /**
   * Handle article click - reorder list and show detail
   */
  const handleArticleClick = (article: InformationArticle) => {
    // Set selected article for detail view
    setSelectedArticle(article);

    // Reorder list: move clicked article to top
    const newList = [article, ...articles.filter(a => a.id !== article.id)];
    setArticles(newList);
  };

  /**
   * Handle like article
   */
  const handleLikeArticle = async (article: InformationArticle) => {
    if (!isOnline) {
      alert('Fitur like memerlukan koneksi internet');
      return;
    }

    try {
      const response = await informationApi.likeArticle(article.slug);
      if (response.success) {
        // Update the article's like count
        const updatedArticles = articles.map(a => 
          a.id === article.id ? { ...a, like_count: response.like_count } : a
        );
        setArticles(updatedArticles);
        
        if (selectedArticle && selectedArticle.id === article.id) {
          setSelectedArticle({ ...selectedArticle, like_count: response.like_count });
        }
      }
    } catch (error) {
      console.error('[InformationEnhanced] Error liking article:', error);
      alert('Gagal memberikan like pada artikel');
    }
  };

  /**
   * Format date for display
   */
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  /**
   * Get excerpt or truncated content
   */
  const getExcerpt = (article: InformationArticle): string => {
    if (article.excerpt) return article.excerpt;
    return article.content.length > 150 
      ? article.content.substring(0, 150) + '...'
      : article.content;
  };

  return (
    <div className={`information-container ${className}`}>
      {/* Universal Header */}
      <UniversalHeader />

      {/* Filters */}
      <div className="information-filters">
        <div className="filter-row">
          <select 
            value={selectedCategory} 
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="filter-select"
          >
            <option value="">Semua Kategori</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
          
          <input
            type="text"
            placeholder="Cari artikel..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="filter-search"
          />
          
          {!isOnline && (
            <div className="offline-indicator">
              <i className="fas fa-wifi-slash"></i> Offline
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <main className="information-main">
        {loading ? (
          <div className="information-loading">
            <div className="loading-spinner"></div>
            <p>Memuat artikel...</p>
          </div>
        ) : error ? (
          <div className="information-error">
            <i className="fas fa-exclamation-triangle"></i>
            <p>{error}</p>
            <button onClick={loadArticles} className="retry-button">
              Coba Lagi
            </button>
          </div>
        ) : (
          <div className="information-layout">
            {/* Left Section - Articles List */}
            <section className="information-list-section" aria-label="Daftar artikel informasi">
              <div className="information-list" role="list">
                {articles.map((article) => (
                  <article
                    key={article.id}
                    className={`information-item ${selectedArticle?.id === article.id ? 'active' : ''}`}
                    onClick={() => handleArticleClick(article)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleArticleClick(article);
                      }
                    }}
                    role="listitem"
                    tabIndex={0}
                    aria-label={`Artikel: ${article.title}`}
                    aria-selected={selectedArticle?.id === article.id}
                  >
                    <div className="information-item-image">
                      <img
                        src={article.featured_image || '/images/icons/icon-192x192.png'}
                        alt={article.image_alt_text || article.title}
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/images/icons/icon-192x192.png';
                        }}
                      />
                      {article.is_featured && (
                        <div className="featured-badge">
                          <i className="fas fa-star"></i>
                        </div>
                      )}
                    </div>
                    <div className="information-item-content">
                      <h3 className="information-item-title">{article.title}</h3>
                      <p className="information-item-description">{getExcerpt(article)}</p>
                      <div className="information-item-meta">
                        <span className="information-item-date">{formatDate(article.published_at)}</span>
                        <span className="information-item-category">{article.category}</span>
                        <div className="information-item-stats">
                          <span><i className="fas fa-eye"></i> {article.view_count}</span>
                          <span><i className="fas fa-heart"></i> {article.like_count}</span>
                        </div>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            </section>

            {/* Right Section - Detail View */}
            <section className="information-detail-section" aria-label="Detail artikel">
              {selectedArticle ? (
                <div className="information-detail">
                  <div className="information-detail-header">
                    <img
                      src={selectedArticle.featured_image || '/images/icons/icon-192x192.png'}
                      alt={selectedArticle.image_alt_text || selectedArticle.title}
                      className="information-detail-image"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/images/icons/icon-192x192.png';
                      }}
                    />
                    <div className="information-detail-meta">
                      <h2 className="information-detail-title">{selectedArticle.title}</h2>
                      <div className="information-detail-info">
                        <span className="information-detail-date">{formatDate(selectedArticle.published_at)}</span>
                        <span className="information-detail-category">{selectedArticle.category}</span>
                        {selectedArticle.author && (
                          <span className="information-detail-author">oleh {selectedArticle.author}</span>
                        )}
                      </div>
                      <div className="information-detail-actions">
                        <button 
                          onClick={() => handleLikeArticle(selectedArticle)}
                          className="like-button"
                          disabled={!isOnline}
                        >
                          <i className="fas fa-heart"></i> {selectedArticle.like_count}
                        </button>
                        <span className="view-count">
                          <i className="fas fa-eye"></i> {selectedArticle.view_count}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="information-detail-content">
                    {selectedArticle.content.split('\n').map((paragraph, index) => (
                      <p key={index} className="information-detail-paragraph">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                  {selectedArticle.tags && selectedArticle.tags.length > 0 && (
                    <div className="information-detail-tags">
                      {selectedArticle.tags.map((tag, index) => (
                        <span key={index} className="tag">#{tag}</span>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="information-detail-placeholder">
                  <div className="placeholder-content">
                    <div className="placeholder-icon">📰</div>
                    <h3 className="placeholder-title">Pilih Artikel</h3>
                    <p className="placeholder-text">
                      Klik salah satu artikel di sebelah kiri untuk melihat detail lengkapnya
                    </p>
                  </div>
                </div>
              )}
            </section>
          </div>
        )}
      </main>

      {/* Universal Home Button */}
      <HomeButton />
    </div>
  );
};

export default InformationEnhanced;
