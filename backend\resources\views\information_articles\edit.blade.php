@extends('layouts.app')

@section('title', 'Edit Artikel Informasi')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-edit"></i> Edit Artikel Informasi
    </h1>
    <div>
        <a href="{{ route('information_articles.show', $informationArticle) }}" class="btn btn-info me-2">
            <i class="fas fa-eye"></i> Lihat
        </a>
        <a href="{{ route('information_articles.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Informasi Artikel</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('information_articles.update', $informationArticle) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <!-- Title -->
                    <div class="mb-3">
                        <label for="title" class="form-label">Judul Artikel <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" value="{{ old('title', $informationArticle->title) }}" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Excerpt -->
                    <div class="mb-3">
                        <label for="excerpt" class="form-label">Ringkasan/Excerpt</label>
                        <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                                  id="excerpt" name="excerpt" rows="3" 
                                  placeholder="Ringkasan singkat artikel (opsional)">{{ old('excerpt', $informationArticle->excerpt) }}</textarea>
                        @error('excerpt')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Maksimal 500 karakter</div>
                    </div>

                    <!-- Content -->
                    <div class="mb-3">
                        <label for="content" class="form-label">Konten Artikel <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('content') is-invalid @enderror" 
                                  id="content" name="content" rows="15" required>{{ old('content', $informationArticle->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Gunakan format teks biasa atau Markdown</div>
                    </div>

                    <!-- Featured Image -->
                    <div class="mb-3">
                        <label for="featured_image" class="form-label">URL Gambar Utama</label>
                        <input type="url" class="form-control @error('featured_image') is-invalid @enderror" 
                               id="featured_image" name="featured_image" value="{{ old('featured_image', $informationArticle->featured_image) }}"
                               placeholder="https://example.com/image.jpg">
                        @error('featured_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        @if($informationArticle->featured_image)
                            <div class="mt-2">
                                <img src="{{ $informationArticle->featured_image }}" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                            </div>
                        @endif
                    </div>

                    <!-- Image Alt Text -->
                    <div class="mb-3">
                        <label for="image_alt_text" class="form-label">Alt Text Gambar</label>
                        <input type="text" class="form-control @error('image_alt_text') is-invalid @enderror" 
                               id="image_alt_text" name="image_alt_text" value="{{ old('image_alt_text', $informationArticle->image_alt_text) }}"
                               placeholder="Deskripsi gambar untuk aksesibilitas">
                        @error('image_alt_text')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Author -->
                    <div class="mb-3">
                        <label for="author" class="form-label">Penulis</label>
                        <input type="text" class="form-control @error('author') is-invalid @enderror" 
                               id="author" name="author" value="{{ old('author', $informationArticle->author) }}"
                               placeholder="Nama penulis artikel">
                        @error('author')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Tags -->
                    <div class="mb-3">
                        <label for="tags" class="form-label">Tags</label>
                        <input type="text" class="form-control @error('tags') is-invalid @enderror" 
                               id="tags" name="tags" value="{{ old('tags', is_array($informationArticle->tags) ? implode(', ', $informationArticle->tags) : '') }}"
                               placeholder="tag1, tag2, tag3">
                        @error('tags')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Pisahkan dengan koma</div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Artikel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Article Stats -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">Statistik Artikel</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-0">{{ number_format($informationArticle->view_count) }}</h4>
                            <small class="text-muted">Views</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-danger mb-0">{{ number_format($informationArticle->like_count) }}</h4>
                        <small class="text-muted">Likes</small>
                    </div>
                </div>
                <hr>
                <div class="small text-muted">
                    <div><strong>Dibuat:</strong> {{ $informationArticle->created_at->format('d M Y H:i') }}</div>
                    <div><strong>Diupdate:</strong> {{ $informationArticle->updated_at->format('d M Y H:i') }}</div>
                    @if($informationArticle->reading_time)
                        <div><strong>Waktu Baca:</strong> ~{{ $informationArticle->reading_time }} menit</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Publishing Options -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">Opsi Publikasi</h6>
            </div>
            <div class="card-body">
                <!-- Status -->
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                        <option value="draft" {{ old('status', $informationArticle->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                        <option value="published" {{ old('status', $informationArticle->status) == 'published' ? 'selected' : '' }}>Published</option>
                        <option value="archived" {{ old('status', $informationArticle->status) == 'archived' ? 'selected' : '' }}>Archived</option>
                    </select>
                    @error('status')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Published Date -->
                <div class="mb-3">
                    <label for="published_at" class="form-label">Tanggal Publikasi</label>
                    <input type="datetime-local" class="form-control @error('published_at') is-invalid @enderror" 
                           id="published_at" name="published_at" 
                           value="{{ old('published_at', $informationArticle->published_at ? $informationArticle->published_at->format('Y-m-d\TH:i') : '') }}">
                    @error('published_at')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">Kosongkan untuk menggunakan waktu sekarang</div>
                </div>

                <!-- Category -->
                <div class="mb-3">
                    <label for="category" class="form-label">Kategori <span class="text-danger">*</span></label>
                    <select class="form-select @error('category') is-invalid @enderror" id="category" name="category" required>
                        <option value="">Pilih Kategori</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}" {{ old('category', $informationArticle->category) == $category ? 'selected' : '' }}>
                                {{ $category }}
                            </option>
                        @endforeach
                    </select>
                    @error('category')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Priority -->
                <div class="mb-3">
                    <label for="priority" class="form-label">Prioritas <span class="text-danger">*</span></label>
                    <input type="number" class="form-control @error('priority') is-invalid @enderror" 
                           id="priority" name="priority" value="{{ old('priority', $informationArticle->priority) }}" 
                           min="0" max="999" required>
                    @error('priority')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">Angka lebih tinggi = prioritas lebih tinggi</div>
                </div>

                <!-- Featured -->
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1"
                               {{ old('is_featured', $informationArticle->is_featured) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_featured">
                            <i class="fas fa-star text-warning"></i> Artikel Unggulan
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- SEO Options -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">SEO & Metadata</h6>
            </div>
            <div class="card-body">
                <!-- Slug -->
                <div class="mb-3">
                    <label for="slug" class="form-label">Slug URL</label>
                    <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                           id="slug" name="slug" value="{{ old('slug', $informationArticle->slug) }}"
                           placeholder="url-friendly-slug">
                    @error('slug')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">Kosongkan untuk auto-generate dari judul</div>
                </div>

                <!-- Meta Title -->
                <div class="mb-3">
                    <label for="meta_title" class="form-label">Meta Title</label>
                    <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                           id="meta_title" name="meta_title" value="{{ old('meta_title', $informationArticle->meta_title) }}"
                           placeholder="Judul untuk SEO">
                    @error('meta_title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Meta Description -->
                <div class="mb-3">
                    <label for="meta_description" class="form-label">Meta Description</label>
                    <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                              id="meta_description" name="meta_description" rows="3"
                              placeholder="Deskripsi untuk SEO">{{ old('meta_description', $informationArticle->meta_description) }}</textarea>
                    @error('meta_description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">Maksimal 500 karakter</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
