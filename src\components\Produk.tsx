import React, { useState, useEffect } from 'react';
import './Produk.css';
import UniversalHeader from './UniversalHeader';
import HomeButton from './HomeButton';

/**
 * Interface untuk data produk dari <PERSON>vel API
 */
interface Product {
  id: number;
  name: string;
  category: string;
  image: string;
  description: string;
  price: number | null;
  merek: string | null;
  unit_model: string | null;
  warranty: string | null;
  discount: string | null;
  notes: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Interface untuk API response
 */
interface ApiResponse {
  success: boolean;
  data: Product[];
}

/**
 * Props untuk komponen Produk
 */
interface ProdukProps {
  className?: string;
}

/**
 * Error Boundary untuk menangkap error dalam rendering produk
 */
class ProductErrorBoundary extends React.Component<
  { children: React.ReactNode; productId?: number },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; productId?: number }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Product rendering error:', error, errorInfo, 'Product ID:', this.props.productId);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="product-card card bg-base-100 p-4 border-error">
          <div className="text-center text-error">
            <p className="text-sm font-semibold">Error loading product</p>
            <p className="text-xs opacity-70">ID: {this.props.productId || 'Unknown'}</p>
            <p className="text-xs opacity-50 mt-1">{this.state.error?.message}</p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Komponen Produk dengan layout grid responsif
 * Menampilkan daftar produk dari Laravel API
 */
const Produk: React.FC<ProdukProps> = ({ className = '' }) => {
  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isUsingFallbackData, setIsUsingFallbackData] = useState<boolean>(false);
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);

  // API base URL - using 127.0.0.1 to match Laravel server
  const API_BASE_URL = 'http://127.0.0.1:8000/api/v1';

  // Online/offline event listeners
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      console.log('Device is online');
    };

    const handleOffline = () => {
      setIsOnline(false);
      console.log('Device is offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Fetch products from Laravel API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Fetching products from:', `${API_BASE_URL}/products`);

        const response = await fetch(`${API_BASE_URL}/products`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          mode: 'cors',
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Response error text:', errorText);
          throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
        }

        const data: ApiResponse = await response.json();
        console.log('API Response:', data);

        if (data.success && Array.isArray(data.data)) {
          setProducts(data.data);
          console.log('Products loaded successfully:', data.data.length, 'items');
          console.log('API products:', data.data);
          setError(null); // Clear any previous errors
          setIsUsingFallbackData(false); // Using real API data
        } else {
          throw new Error('API returned unsuccessful response or invalid data format');
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch products';

        // Try to use fallback data if API fails
        console.log('API failed, attempting to use fallback data...');
        const fallbackProducts: Product[] = [
          {
            id: 1,
            name: 'COMPRESSOR',
            category: 'AC Parts',
            image: '/images/product/img1.png',
            description: 'Kompressor AC berkualitas tinggi untuk berbagai jenis kendaraan',
            price: 6000000,
            merek: 'SANNY',
            unit_model: 'SY500',
            warranty: '3 Bulan',
            discount: null,
            notes: 'Warranty 3 Bulan',
            is_active: true,
            created_at: '2025-08-12T14:04:00.000000Z',
            updated_at: '2025-08-12T14:04:00.000000Z',
          },
          {
            id: 2,
            name: 'EVAPORATOR',
            category: 'AC Parts',
            image: '/images/product/img2.png',
            description: 'Evaporator AC dengan teknologi terbaru untuk pendinginan optimal',
            price: 2500000,
            merek: 'DENSO',
            unit_model: 'EV-200',
            warranty: '2 Tahun',
            discount: null,
            notes: null,
            is_active: true,
            created_at: '2025-08-12T14:04:00.000000Z',
            updated_at: '2025-08-12T14:04:00.000000Z',
          },
          {
            id: 3,
            name: 'CONDENSER',
            category: 'AC Parts',
            image: '/images/product/img1.png',
            description: 'Kondenser AC dengan material berkualitas untuk transfer panas optimal',
            price: 3200000,
            merek: 'DENSO',
            unit_model: 'CD-500',
            warranty: '2 Tahun',
            discount: null,
            notes: null,
            is_active: true,
            created_at: '2025-08-12T14:04:00.000000Z',
            updated_at: '2025-08-12T14:04:00.000000Z',
          },
        ];

        setProducts(fallbackProducts);
        setIsUsingFallbackData(true); // Mark as using fallback data
        setError(`API tidak tersedia: ${errorMessage}`);
        console.log('Fallback data loaded:', fallbackProducts.length, 'items');
        console.log('Fallback products:', fallbackProducts);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [API_BASE_URL]);

  /**
   * Fungsi untuk menangani klik tombol "Lihat Detail"
   * @param productId - ID produk yang dipilih
   */
  const handleViewProductDetail = (productId: number) => {
    setSelectedProduct(selectedProduct === productId ? null : productId);
  };

  /**
   * Fungsi untuk mendapatkan produk yang dipilih
   */
  const getSelectedProduct = () => {
    return products.find(product => product.id === selectedProduct);
  };

  /**
   * Format harga dalam Rupiah
   */
  const formatPrice = (price: number | null | undefined) => {
    if (!price || price === null || price === undefined) return 'Harga tidak tersedia';
    return `Rp ${price.toLocaleString('id-ID')}`;
  };

  /**
   * Handle image loading errors with fallback
   */
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const img = e.target as HTMLImageElement;
    if (img.src !== '/images/icons/icon-192x192.png') {
      console.log('Product image failed to load:', img.src, 'Using fallback image');
      img.src = '/images/icons/icon-192x192.png';
    }
  };

  /**
   * Get safe product property with fallback
   */
  const getSafeProperty = (value: any, fallback: string = 'Tidak tersedia') => {
    return value && value !== null && value !== undefined ? value : fallback;
  };

  // Debug logging
  console.log('Produk component render state:', {
    loading,
    error,
    productsCount: products.length,
    isOnline,
    isUsingFallbackData,
    selectedProduct
  });

  // Loading state
  if (loading) {
    console.log('Rendering loading state');
    return (
      <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
        <UniversalHeader />
        <div className="container mx-auto p-4 flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="loading loading-spinner loading-lg text-primary"></div>
            <p className="mt-4 text-base-content">Memuat produk...</p>
          </div>
        </div>
        <HomeButton />
      </div>
    );
  }

  // Error state - only show error UI if no products are available
  if (error && products.length === 0) {
    return (
      <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
        <UniversalHeader />
        <div className="container mx-auto p-4 flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <div className="alert alert-error">
              <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <div className="font-bold">Koneksi API Bermasalah</div>
                <div className="text-sm mt-1">{error}</div>
              </div>
            </div>
            <div className="mt-4 text-sm text-base-content opacity-70">
              <p>API URL: {API_BASE_URL}/products</p>
              <p>Pastikan Laravel server berjalan di port 8000</p>
            </div>
            <div className="mt-4 space-x-2">
              <button
                className="btn btn-primary btn-sm"
                onClick={() => window.location.reload()}
              >
                Coba Lagi
              </button>
              <button
                className="btn btn-outline btn-sm"
                onClick={() => window.open(`${API_BASE_URL}/products`, '_blank')}
              >
                Test API
              </button>
            </div>
          </div>
        </div>
        <HomeButton />
      </div>
    );
  }

  return (
    <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
      {/* Universal Header */}
      <UniversalHeader />

      {/* Status Banner */}
      {(error && products.length > 0) && (
        <div className={`px-4 py-2 text-sm flex items-center justify-between ${
          !isOnline
            ? 'bg-error text-error-content'
            : isUsingFallbackData
              ? 'bg-warning text-warning-content'
              : 'bg-info text-info-content'
        }`}>
          <div className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span>
              {!isOnline
                ? 'Offline - Menampilkan data tersimpan'
                : isUsingFallbackData
                  ? 'API tidak tersedia - Menampilkan data contoh'
                  : 'Menggunakan data tersimpan'
              }
            </span>
          </div>
          <button
            className="btn btn-xs btn-ghost"
            onClick={() => window.location.reload()}
          >
            Coba Lagi
          </button>
        </div>
      )}

      {/* Main Content - Flexible height */}
      <div className="container mx-auto p-4 flex-1 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
          {/* Grid Produk */}
          <div className="lg:col-span-2 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-2">
              {Array.isArray(products) && products.length > 0 ? products.map((product) => {
                try {
                  // Ensure product has required properties
                  if (!product || !product.id) {
                    console.warn('Invalid product data:', product);
                    return null;
                  }

                  return (
                    <ProductErrorBoundary key={product.id} productId={product.id}>
                      <div
                        className={`product-card card bg-base-100 hover:shadow-xl transition-all duration-300 ${
                          selectedProduct === product.id ? 'ring-2 ring-primary' : ''
                        }`}
                      >
                  {/* Gambar Produk */}
                  <figure className="px-2 pt-2">
                    <img
                      src={getSafeProperty(product.image, '/images/icons/icon-192x192.png')}
                      alt={getSafeProperty(product.name, 'Produk')}
                      className="rounded-lg w-full h-20 object-cover"
                      loading="lazy"
                      onError={handleImageError}
                    />
                  </figure>

                  {/* Konten Card */}
                  <div className="card-body p-4">
                    <h2 className="card-title text-lg font-bold text-primary">
                      {getSafeProperty(product.name, 'Nama Produk Tidak Tersedia')}
                    </h2>
                    <p className="text-xs text-base-content opacity-70 mb-2">
                      {getSafeProperty(product.description, 'Deskripsi tidak tersedia')}
                    </p>
                    <div className="badge badge-secondary badge-sm mb-3">
                      {getSafeProperty(product.category, 'Kategori')}
                    </div>

                    {/* Harga */}
                    <div className="text-sm font-semibold text-primary mb-2">
                      {formatPrice(product.price)}
                    </div>

                    {/* Tombol Lihat Detail */}
                    <div className="card-actions justify-end">
                      <button
                        className={`btn btn-primary btn-sm ${
                          selectedProduct === product.id ? 'btn-active' : ''
                        }`}
                        onClick={() => handleViewProductDetail(product.id)}
                      >
                        {selectedProduct === product.id ? 'Tutup Detail' : 'Lihat Detail'}
                      </button>
                    </div>
                  </div>
                </div>
                    </ProductErrorBoundary>
                  );
                } catch (error) {
                  console.error('Error rendering product:', product?.id, error);
                  return (
                    <ProductErrorBoundary key={product?.id || Math.random()} productId={product?.id}>
                      <div className="product-card card bg-base-100 p-4">
                        <div className="text-center text-error">
                          <p className="text-sm">Error loading product</p>
                          <p className="text-xs opacity-70">ID: {product?.id || 'Unknown'}</p>
                        </div>
                      </div>
                    </ProductErrorBoundary>
                  );
                }
              }) : (
                <div className="col-span-full text-center py-8">
                  <div className="text-base-content opacity-70">
                    <p className="text-lg mb-2">Tidak ada produk untuk ditampilkan</p>
                    <p className="text-sm">
                      {!Array.isArray(products) ? 'Data produk tidak valid' : 'Daftar produk kosong'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Detail Produk */}
          <div className="lg:col-span-1 overflow-hidden">
            <div className={`product-detail-container h-full transition-all duration-500 ${
              selectedProduct ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'
            }`}>
              {selectedProduct && getSelectedProduct() && (
                <div className="card bg-base-100 shadow-lg h-full flex flex-col">
                  <div className="card-body p-4 flex-1 overflow-hidden flex flex-col">
                    {/* Header Detail */}
                    <div className="flex items-center gap-3 mb-4 flex-shrink-0">
                      <img
                        src={getSafeProperty(getSelectedProduct()?.image, '/images/icons/icon-192x192.png')}
                        alt={getSafeProperty(getSelectedProduct()?.name, 'Produk')}
                        className="w-12 h-12 rounded-lg object-cover"
                        onError={handleImageError}
                      />
                      <div>
                        <h3 className="text-lg font-bold text-primary">
                          {getSafeProperty(getSelectedProduct()?.name, 'Nama Produk Tidak Tersedia')}
                        </h3>
                        <div className="badge badge-secondary badge-sm">
                          {getSelectedProduct()!.category}
                        </div>
                      </div>
                    </div>

                    {/* Detail Content */}
                    <div className="flex-1 overflow-y-auto">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-sm mb-1">Deskripsi:</h4>
                          <p className="text-xs text-base-content opacity-80">
                            {getSafeProperty(getSelectedProduct()?.description, 'Deskripsi tidak tersedia')}
                          </p>
                        </div>

                        <div>
                          <h4 className="font-semibold text-sm mb-1">Harga:</h4>
                          <p className="text-lg font-bold text-primary">
                            {formatPrice(getSelectedProduct()?.price)}
                          </p>
                          {getSelectedProduct()?.discount && (
                            <p className="text-xs text-success">
                              {getSafeProperty(getSelectedProduct()?.discount, '')}
                            </p>
                          )}
                        </div>

                        {getSelectedProduct()?.merek && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Merek:</h4>
                            <p className="text-sm">{getSafeProperty(getSelectedProduct()?.merek, 'Tidak tersedia')}</p>
                          </div>
                        )}

                        {getSelectedProduct()!.unit_model && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Model:</h4>
                            <p className="text-sm">{getSelectedProduct()!.unit_model}</p>
                          </div>
                        )}

                        {getSelectedProduct()!.warranty && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Garansi:</h4>
                            <div className="badge badge-info badge-sm">
                              {getSelectedProduct()!.warranty}
                            </div>
                          </div>
                        )}

                        {getSelectedProduct()!.notes && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Catatan:</h4>
                            <p className="text-xs text-base-content opacity-80">
                              {getSelectedProduct()!.notes}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Home Button */}
      <HomeButton />
    </div>
  );
};

export default Produk;
