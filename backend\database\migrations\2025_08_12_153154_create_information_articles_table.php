<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('information_articles', function (Blueprint $table) {
            $table->id();

            // Basic article information
            $table->string('title');
            $table->text('excerpt')->nullable(); // Short summary/description
            $table->longText('content'); // Full article content

            // Media and visual
            $table->string('featured_image')->nullable();
            $table->string('image_alt_text')->nullable();

            // Author and publication info
            $table->string('author')->nullable();
            $table->timestamp('published_at')->nullable();

            // Categorization
            $table->string('category');
            $table->json('tags')->nullable(); // Store tags as JSON array

            // Status and visibility
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->boolean('is_featured')->default(false);

            // Priority ordering (higher number = higher priority)
            $table->integer('priority')->default(0);

            // SEO and metadata
            $table->string('slug')->unique()->nullable();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();

            // Engagement metrics
            $table->integer('view_count')->default(0);
            $table->integer('like_count')->default(0);

            // Timestamps
            $table->timestamps();

            // Indexes for performance
            $table->index(['status', 'published_at']);
            $table->index(['category', 'status']);
            $table->index(['priority', 'published_at']);
            $table->index('is_featured');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('information_articles');
    }
};
