<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;

class Reminder extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'description',
        'reminder_date',
        'reminder_time',
        'status',
        'priority',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'reminder_date' => 'date',
        'reminder_time' => 'datetime:H:i',
        'metadata' => 'array',
        'priority' => 'integer',
        'user_id' => 'integer',
    ];

    /**
     * Scope a query to only include pending reminders.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include completed reminders.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to order by priority and date.
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc')
                    ->orderBy('reminder_date', 'asc')
                    ->orderBy('reminder_time', 'asc');
    }

    /**
     * Scope a query to get upcoming reminders.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('reminder_date', '>=', now()->toDateString())
                    ->where('status', 'pending');
    }

    /**
     * Scope a query to get overdue reminders.
     */
    public function scopeOverdue($query)
    {
        $now = now();
        return $query->where(function ($q) use ($now) {
            $q->where('reminder_date', '<', $now->toDateString())
              ->orWhere(function ($q2) use ($now) {
                  $q2->where('reminder_date', '=', $now->toDateString())
                     ->where('reminder_time', '<', $now->format('H:i:s'));
              });
        })->where('status', 'pending');
    }

    /**
     * Scope a query to search reminders by title or description.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * Mark reminder as completed.
     */
    public function markAsCompleted()
    {
        $this->update(['status' => 'completed']);
    }

    /**
     * Mark reminder as cancelled.
     */
    public function markAsCancelled()
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Get formatted reminder datetime.
     */
    protected function formattedDateTime(): Attribute
    {
        return Attribute::make(
            get: fn () => Carbon::parse($this->reminder_date . ' ' . $this->reminder_time)->format('d M Y, H:i'),
        );
    }

    /**
     * Check if reminder is overdue.
     */
    protected function isOverdue(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->status !== 'pending') {
                    return false;
                }

                $reminderDateTime = Carbon::parse($this->reminder_date . ' ' . $this->reminder_time);
                return $reminderDateTime->isPast();
            }
        );
    }

    /**
     * Get priority label.
     */
    protected function priorityLabel(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->priority) {
                5 => 'Sangat Tinggi',
                4 => 'Tinggi',
                3 => 'Sedang',
                2 => 'Rendah',
                1 => 'Sangat Rendah',
                default => 'Sedang'
            }
        );
    }

    /**
     * Get status label in Indonesian.
     */
    protected function statusLabel(): Attribute
    {
        return Attribute::make(
            get: fn () => match($this->status) {
                'pending' => 'Menunggu',
                'completed' => 'Selesai',
                'cancelled' => 'Dibatalkan',
                default => 'Menunggu'
            }
        );
    }
}
