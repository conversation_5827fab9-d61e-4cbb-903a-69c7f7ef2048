<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Reminder System - PPWA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Reminder System - PPWA</h1>
    <p>Comprehensive testing untuk sistem reminder yang terintegrasi dengan Laravel backend dan React PWA frontend.</p>

    <div class="test-section">
        <h2>📊 1. Test API Connectivity</h2>
        <p>Test koneksi ke Laravel backend API</p>
        <button onclick="testApiConnectivity()">Test API Connection</button>
        <div id="api-connectivity-result"></div>
    </div>

    <div class="test-section">
        <h2>📝 2. Test Reminders CRUD</h2>
        <p>Test operasi Create, Read, Update, Delete untuk reminders</p>
        <button onclick="testCreateReminder()">Test Create Reminder</button>
        <button onclick="testGetReminders()">Test Get Reminders</button>
        <button onclick="testUpdateReminder()">Test Update Reminder</button>
        <button onclick="testDeleteReminder()">Test Delete Reminder</button>
        <div id="crud-result"></div>
    </div>

    <div class="test-section">
        <h2>📈 3. Test Reminder Statistics</h2>
        <p>Test endpoint statistik reminder</p>
        <button onclick="testReminderStats()">Test Statistics</button>
        <div id="stats-result"></div>
    </div>

    <div class="test-section">
        <h2>✅ 4. Test Status Actions</h2>
        <p>Test marking reminders as completed/cancelled</p>
        <button onclick="testMarkCompleted()">Test Mark Completed</button>
        <button onclick="testMarkCancelled()">Test Mark Cancelled</button>
        <div id="status-result"></div>
    </div>

    <div class="test-section">
        <h2>🔍 5. Test Information API (Fixed)</h2>
        <p>Test Information API yang sudah diperbaiki</p>
        <button onclick="testInformationApi()">Test Information API</button>
        <div id="information-result"></div>
    </div>

    <div class="test-section">
        <h2>📱 6. Test PWA Integration</h2>
        <p>Test integrasi dengan React PWA frontend</p>
        <button onclick="testPwaIntegration()">Test PWA Integration</button>
        <div id="pwa-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        let testReminderId = null;

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        async function testApiConnectivity() {
            try {
                const response = await fetch(`${API_BASE_URL}/reminders-stats`);
                const data = await response.json();
                
                if (data.success) {
                    showResult('api-connectivity-result', 
                        `✅ API Connection successful!<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    showResult('api-connectivity-result', '❌ API returned error', 'error');
                }
            } catch (error) {
                showResult('api-connectivity-result', `❌ Connection failed: ${error.message}`, 'error');
            }
        }

        async function testCreateReminder() {
            try {
                const reminderData = {
                    title: 'Test Reminder dari HTML',
                    description: 'Ini adalah test reminder yang dibuat dari test file',
                    reminder_date: '2025-08-15',
                    reminder_time: '09:00',
                    priority: 4
                };

                const response = await fetch(`${API_BASE_URL}/reminders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(reminderData)
                });

                const data = await response.json();
                
                if (data.success) {
                    testReminderId = data.data.id;
                    showResult('crud-result', 
                        `✅ Reminder created successfully!<br>
                        ID: ${data.data.id}<br>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>`, 'success');
                } else {
                    showResult('crud-result', `❌ Create failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('crud-result', `❌ Create failed: ${error.message}`, 'error');
            }
        }

        async function testGetReminders() {
            try {
                const response = await fetch(`${API_BASE_URL}/reminders`);
                const data = await response.json();
                
                if (data.success) {
                    showResult('crud-result', 
                        `✅ Reminders retrieved successfully!<br>
                        Total: ${data.data.length}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    showResult('crud-result', '❌ Get reminders failed', 'error');
                }
            } catch (error) {
                showResult('crud-result', `❌ Get reminders failed: ${error.message}`, 'error');
            }
        }

        async function testUpdateReminder() {
            if (!testReminderId) {
                showResult('crud-result', '❌ Please create a reminder first', 'error');
                return;
            }

            try {
                const updateData = {
                    title: 'Updated Test Reminder',
                    description: 'Reminder yang sudah diupdate',
                    reminder_date: '2025-08-16',
                    reminder_time: '10:30',
                    priority: 5,
                    status: 'pending'
                };

                const response = await fetch(`${API_BASE_URL}/reminders/${testReminderId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult('crud-result', 
                        `✅ Reminder updated successfully!<br>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>`, 'success');
                } else {
                    showResult('crud-result', `❌ Update failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('crud-result', `❌ Update failed: ${error.message}`, 'error');
            }
        }

        async function testDeleteReminder() {
            if (!testReminderId) {
                showResult('crud-result', '❌ Please create a reminder first', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/reminders/${testReminderId}`, {
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult('crud-result', 
                        `✅ Reminder deleted successfully!<br>
                        Message: ${data.message}`, 'success');
                    testReminderId = null;
                } else {
                    showResult('crud-result', `❌ Delete failed: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('crud-result', `❌ Delete failed: ${error.message}`, 'error');
            }
        }

        async function testReminderStats() {
            try {
                const response = await fetch(`${API_BASE_URL}/reminders-stats`);
                const data = await response.json();
                
                if (data.success) {
                    showResult('stats-result', 
                        `✅ Statistics retrieved successfully!<br>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>`, 'success');
                } else {
                    showResult('stats-result', '❌ Stats retrieval failed', 'error');
                }
            } catch (error) {
                showResult('stats-result', `❌ Stats retrieval failed: ${error.message}`, 'error');
            }
        }

        async function testMarkCompleted() {
            // First create a reminder to test with
            try {
                const createResponse = await fetch(`${API_BASE_URL}/reminders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        title: 'Test Complete Action',
                        description: 'Test marking as completed',
                        reminder_date: '2025-08-15',
                        reminder_time: '11:00',
                        priority: 3
                    })
                });

                const createData = await createResponse.json();
                if (!createData.success) {
                    showResult('status-result', '❌ Failed to create test reminder', 'error');
                    return;
                }

                const reminderId = createData.data.id;

                // Now mark it as completed
                const completeResponse = await fetch(`${API_BASE_URL}/reminders/${reminderId}/complete`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                const completeData = await completeResponse.json();
                
                if (completeData.success) {
                    showResult('status-result', 
                        `✅ Reminder marked as completed!<br>
                        Status: ${completeData.data.status}<br>
                        <pre>${JSON.stringify(completeData.data, null, 2)}</pre>`, 'success');
                } else {
                    showResult('status-result', `❌ Mark completed failed: ${completeData.message}`, 'error');
                }
            } catch (error) {
                showResult('status-result', `❌ Mark completed failed: ${error.message}`, 'error');
            }
        }

        async function testMarkCancelled() {
            // First create a reminder to test with
            try {
                const createResponse = await fetch(`${API_BASE_URL}/reminders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        title: 'Test Cancel Action',
                        description: 'Test marking as cancelled',
                        reminder_date: '2025-08-15',
                        reminder_time: '12:00',
                        priority: 2
                    })
                });

                const createData = await createResponse.json();
                if (!createData.success) {
                    showResult('status-result', '❌ Failed to create test reminder', 'error');
                    return;
                }

                const reminderId = createData.data.id;

                // Now mark it as cancelled
                const cancelResponse = await fetch(`${API_BASE_URL}/reminders/${reminderId}/cancel`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                const cancelData = await cancelResponse.json();
                
                if (cancelData.success) {
                    showResult('status-result', 
                        `✅ Reminder marked as cancelled!<br>
                        Status: ${cancelData.data.status}<br>
                        <pre>${JSON.stringify(cancelData.data, null, 2)}</pre>`, 'success');
                } else {
                    showResult('status-result', `❌ Mark cancelled failed: ${cancelData.message}`, 'error');
                }
            } catch (error) {
                showResult('status-result', `❌ Mark cancelled failed: ${error.message}`, 'error');
            }
        }

        async function testInformationApi() {
            try {
                const response = await fetch(`${API_BASE_URL}/information`);
                const data = await response.json();
                
                if (data.success) {
                    showResult('information-result', 
                        `✅ Information API working correctly!<br>
                        Articles count: ${data.data.length}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    showResult('information-result', '❌ Information API failed', 'error');
                }
            } catch (error) {
                showResult('information-result', `❌ Information API failed: ${error.message}`, 'error');
            }
        }

        async function testPwaIntegration() {
            try {
                const pwaUrl = 'http://localhost:5173';
                const response = await fetch(pwaUrl);
                
                if (response.ok) {
                    showResult('pwa-result', 
                        `✅ PWA is running successfully!<br>
                        URL: <a href="${pwaUrl}" target="_blank">${pwaUrl}</a><br>
                        Status: ${response.status} ${response.statusText}`, 'success');
                } else {
                    showResult('pwa-result', `❌ PWA not accessible: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('pwa-result', `❌ PWA connection failed: ${error.message}`, 'error');
            }
        }

        // Auto-test API connectivity on page load
        window.addEventListener('load', () => {
            testApiConnectivity();
        });
    </script>
</body>
</html>
