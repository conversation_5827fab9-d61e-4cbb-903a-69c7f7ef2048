# 📰 Information Management System

Sistem manajemen informasi komprehensif dengan <PERSON> backend (CRUD lengkap) dan <PERSON>act PWA frontend (view-only) dengan fitur offline-first.

## 🏗️ Arsitektur Sistem

### Backend (Laravel)
- **Framework**: Laravel 11 dengan SQLite database
- **Pattern**: MVC (Model-View-Controller)
- **API**: RESTful API untuk PWA frontend
- **Admin Interface**: Blade templates dengan Bootstrap

### Frontend (React PWA)
- **Framework**: React 19 dengan TypeScript
- **Styling**: Tailwind CSS + DaisyUI
- **Offline**: IndexedDB untuk caching
- **Mobile**: Capacitor untuk deployment Android

## 📊 Database Schema

### Tabel: `information_articles`

| Field | Type | Description |
|-------|------|-------------|
| `id` | Primary Key | Auto-increment ID |
| `title` | String | Judul artikel |
| `excerpt` | Text (nullable) | Ringkasan artikel |
| `content` | LongText | Konten lengkap artikel |
| `featured_image` | String (nullable) | URL gambar utama |
| `image_alt_text` | String (nullable) | Alt text untuk gambar |
| `author` | String (nullable) | Nama penulis |
| `published_at` | Timestamp (nullable) | Tanggal publikasi |
| `category` | String | Kategori artikel |
| `tags` | JSON (nullable) | Array tags |
| `status` | Enum | draft, published, archived |
| `is_featured` | Boolean | Artikel unggulan |
| `priority` | Integer | Prioritas (0-999, tinggi = prioritas tinggi) |
| `slug` | String (unique) | URL-friendly identifier |
| `meta_title` | String (nullable) | SEO title |
| `meta_description` | Text (nullable) | SEO description |
| `view_count` | Integer | Jumlah views |
| `like_count` | Integer | Jumlah likes |
| `created_at` | Timestamp | Waktu dibuat |
| `updated_at` | Timestamp | Waktu diupdate |

### Indexes untuk Performance
- `status + published_at`
- `category + status`
- `priority + published_at`
- `is_featured`
- `slug` (unique)

## 🔗 API Endpoints

### Base URL: `http://localhost:8000/api/v1`

| Method | Endpoint | Description | Parameters |
|--------|----------|-------------|------------|
| GET | `/information` | Get all published articles | `category`, `featured`, `search`, `per_page`, `page` |
| GET | `/information/{slug}` | Get specific article by slug | - |
| GET | `/information-categories` | Get available categories | - |
| POST | `/information/{slug}/like` | Like an article | - |

### Response Format
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "current_page": 1,
    "last_page": 1,
    "per_page": 10,
    "total": 3,
    "has_more": false
  }
}
```

## 🎯 Priority-Based Ordering

Artikel diurutkan berdasarkan:
1. **Priority** (descending) - Angka lebih tinggi = prioritas lebih tinggi
2. **Published Date** (descending) - Artikel terbaru lebih dulu

### Contoh Pengurutan:
- Priority 100: Artikel Penting (muncul pertama)
- Priority 90: Artikel Unggulan
- Priority 80: Artikel Biasa
- Priority 0: Artikel dengan prioritas rendah

## 🖥️ Admin Interface (Laravel Blade)

### URL: `http://localhost:8000`

#### Fitur CRUD Lengkap:
- **Create**: Form lengkap dengan validasi
- **Read**: Daftar artikel dengan pagination dan statistik
- **Update**: Edit artikel dengan preview
- **Delete**: Hapus artikel dengan konfirmasi

#### Fitur Tambahan:
- Filter dan pencarian
- Bulk actions
- Statistics dashboard
- SEO metadata management
- Image preview
- Priority management

## 📱 PWA Frontend (React)

### URL: `http://localhost:5173`

#### Fitur View-Only:
- Daftar artikel dengan prioritas ordering
- Detail artikel dengan konten lengkap
- Filter berdasarkan kategori
- Pencarian artikel
- Like functionality
- Responsive design untuk tablet

#### Offline-First Features:
- Automatic caching dengan IndexedDB
- Offline indicator
- Fallback ke cache saat offline
- Background sync saat online kembali

## 🚀 Installation & Setup

### 1. Backend Setup (Laravel)
```bash
cd backend
composer install
php artisan migrate
php artisan db:seed --class=InformationArticleSeeder
php artisan serve --host=0.0.0.0 --port=8000
```

### 2. Frontend Setup (React PWA)
```bash
npm install
npm run dev
```

### 3. Android Build (Optional)
```bash
npm run cap:build
npm run cap:android
```

## 🧪 Testing

### Test Suite: `test-information-system.html`

Comprehensive testing meliputi:
1. **API Connectivity** - Test koneksi ke backend
2. **Articles API** - Test endpoint artikel
3. **Priority Ordering** - Validasi pengurutan prioritas
4. **Search & Filter** - Test fitur pencarian
5. **Offline Storage** - Test IndexedDB functionality
6. **Like Feature** - Test fitur like artikel

### Manual Testing Checklist:

#### Backend (Admin Interface):
- [ ] Create artikel baru
- [ ] Edit artikel existing
- [ ] Delete artikel
- [ ] Upload gambar
- [ ] Set priority dan featured status
- [ ] Publish/unpublish artikel
- [ ] View statistics

#### Frontend (PWA):
- [ ] Load artikel dari API
- [ ] Filter berdasarkan kategori
- [ ] Search artikel
- [ ] Like artikel
- [ ] Offline functionality
- [ ] Responsive design
- [ ] Navigation consistency

## 📋 Kategori Default

- Peringatan Nasional
- Promo & Diskon
- Prestasi & Penghargaan
- Tips & Panduan
- Berita Perusahaan
- Teknologi & Inovasi
- Keselamatan Kerja
- Lingkungan
- Kemitraan
- Lainnya

## 🎨 Design Patterns

### Konsistensi dengan PWA Existing:
- Tailwind CSS + DaisyUI components
- UniversalHeader dan HomeButton
- No-scroll layouts
- Indonesian language labels
- Gradient backgrounds
- Consistent color scheme
- Android-compatible design

### Responsive Breakpoints:
- Mobile: < 768px
- Tablet: 768px - 1023px
- Desktop: > 1024px

## 🔧 Configuration

### Environment Variables:
```env
# Backend (.env)
DB_CONNECTION=sqlite
DB_DATABASE=database/database.sqlite

# Frontend (vite.config.ts)
API_BASE_URL=http://localhost:8000/api/v1
```

### Key Files:
- **Backend Model**: `app/Models/InformationArticle.php`
- **Backend Controller**: `app/Http/Controllers/InformationArticleController.php`
- **Frontend Component**: `src/components/InformationEnhanced.tsx`
- **API Service**: `src/utils/informationApi.ts`
- **Storage Service**: `src/utils/storage.ts`

## 📈 Performance Optimizations

### Backend:
- Database indexes untuk query performance
- Pagination untuk large datasets
- Eager loading untuk relationships
- Query scopes untuk reusable filters

### Frontend:
- IndexedDB caching untuk offline access
- Lazy loading untuk images
- Debounced search input
- Optimized re-renders dengan React hooks

## 🔒 Security Considerations

### Backend:
- Input validation dan sanitization
- CSRF protection
- SQL injection prevention
- XSS protection

### Frontend:
- Content Security Policy
- Secure API communication
- Input sanitization
- Error handling

## 🚀 Future Enhancements

### Planned Features:
- [ ] User authentication dan authorization
- [ ] Comment system
- [ ] Social sharing
- [ ] Push notifications
- [ ] Advanced analytics
- [ ] Multi-language support
- [ ] Rich text editor
- [ ] Image upload management
- [ ] Email notifications
- [ ] RSS feed generation

## 📞 Support

Untuk pertanyaan atau issues, silakan hubungi tim development atau buat issue di repository project.

---

**Status**: ✅ Production Ready
**Version**: 1.0.0
**Last Updated**: August 12, 2025
