<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Information Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin-left: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .results {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .article-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .article-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: #fafafa;
        }
        .article-title {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .article-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .priority-badge {
            background: #17a2b8;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
        .featured-badge {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Information Management System - Test Suite</h1>
        <p>Comprehensive testing untuk sistem manajemen informasi dengan Laravel backend dan React PWA frontend.</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>🔗 1. API Connectivity Test</h3>
            <p>Test koneksi ke Laravel backend API</p>
            <button onclick="testApiConnectivity()">Test API Connection</button>
            <span id="api-status"></span>
            <div id="api-results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>📰 2. Information Articles API Test</h3>
            <p>Test endpoint untuk mengambil artikel informasi</p>
            <button onclick="testInformationAPI()">Test Articles API</button>
            <button onclick="testCategoriesAPI()">Test Categories API</button>
            <span id="articles-status"></span>
            <div id="articles-results" class="results"></div>
            <div id="articles-list" class="article-list"></div>
        </div>

        <div class="test-section">
            <h3>🎯 3. Priority Ordering Test</h3>
            <p>Test pengurutan artikel berdasarkan prioritas</p>
            <button onclick="testPriorityOrdering()">Test Priority Ordering</button>
            <span id="priority-status"></span>
            <div id="priority-results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>🔍 4. Search & Filter Test</h3>
            <p>Test fitur pencarian dan filter kategori</p>
            <button onclick="testSearchFilter()">Test Search & Filter</button>
            <span id="filter-status"></span>
            <div id="filter-results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>💾 5. Offline Storage Test</h3>
            <p>Test penyimpanan offline dengan IndexedDB</p>
            <button onclick="testOfflineStorage()">Test Offline Storage</button>
            <span id="storage-status"></span>
            <div id="storage-results" class="results"></div>
        </div>

        <div class="test-section">
            <h3>❤️ 6. Like Functionality Test</h3>
            <p>Test fitur like artikel</p>
            <button onclick="testLikeFunctionality()">Test Like Feature</button>
            <span id="like-status"></span>
            <div id="like-results" class="results"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // Test API Connectivity
        async function testApiConnectivity() {
            const statusEl = document.getElementById('api-status');
            const resultsEl = document.getElementById('api-results');
            
            statusEl.innerHTML = '<span class="warning">Testing...</span>';
            resultsEl.textContent = 'Testing API connectivity...';
            
            try {
                const response = await fetch(`${API_BASE}/information?per_page=1`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    statusEl.innerHTML = '<span class="success">✅ Connected</span>';
                    resultsEl.textContent = `API Response: ${JSON.stringify(data, null, 2)}`;
                } else {
                    throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                statusEl.innerHTML = '<span class="error">❌ Failed</span>';
                resultsEl.textContent = `Error: ${error.message}`;
            }
        }

        // Test Information Articles API
        async function testInformationAPI() {
            const statusEl = document.getElementById('articles-status');
            const resultsEl = document.getElementById('articles-results');
            const listEl = document.getElementById('articles-list');
            
            statusEl.innerHTML = '<span class="warning">Testing...</span>';
            resultsEl.textContent = 'Fetching articles...';
            listEl.innerHTML = '';
            
            try {
                const response = await fetch(`${API_BASE}/information`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    statusEl.innerHTML = '<span class="success">✅ Success</span>';
                    resultsEl.textContent = `Found ${data.data.length} articles\n${JSON.stringify(data.pagination, null, 2)}`;
                    
                    // Display articles
                    data.data.forEach(article => {
                        const articleEl = document.createElement('div');
                        articleEl.className = 'article-card';
                        articleEl.innerHTML = `
                            <div class="article-title">${article.title}</div>
                            <div class="article-meta">
                                <span class="priority-badge">Priority: ${article.priority}</span>
                                ${article.is_featured ? '<span class="featured-badge">Featured</span>' : ''}
                                <br>
                                Category: ${article.category} | Views: ${article.view_count} | Likes: ${article.like_count}
                                <br>
                                Published: ${new Date(article.published_at).toLocaleDateString('id-ID')}
                            </div>
                            <div style="font-size: 13px; color: #666;">
                                ${article.excerpt || article.content.substring(0, 100) + '...'}
                            </div>
                        `;
                        listEl.appendChild(articleEl);
                    });
                } else {
                    throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                statusEl.innerHTML = '<span class="error">❌ Failed</span>';
                resultsEl.textContent = `Error: ${error.message}`;
            }
        }

        // Test Categories API
        async function testCategoriesAPI() {
            try {
                const response = await fetch(`${API_BASE}/information-categories`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    document.getElementById('articles-results').textContent += `\n\nCategories: ${data.data.join(', ')}`;
                }
            } catch (error) {
                console.error('Categories API test failed:', error);
            }
        }

        // Test Priority Ordering
        async function testPriorityOrdering() {
            const statusEl = document.getElementById('priority-status');
            const resultsEl = document.getElementById('priority-results');
            
            statusEl.innerHTML = '<span class="warning">Testing...</span>';
            resultsEl.textContent = 'Testing priority ordering...';
            
            try {
                const response = await fetch(`${API_BASE}/information`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const articles = data.data;
                    let isCorrectOrder = true;
                    let orderingInfo = 'Priority ordering check:\n';
                    
                    for (let i = 0; i < articles.length - 1; i++) {
                        const current = articles[i];
                        const next = articles[i + 1];
                        
                        orderingInfo += `${i + 1}. "${current.title}" (Priority: ${current.priority})\n`;
                        
                        if (current.priority < next.priority) {
                            isCorrectOrder = false;
                            orderingInfo += `   ❌ ERROR: Lower priority than next article!\n`;
                        }
                    }
                    
                    if (articles.length > 0) {
                        const last = articles[articles.length - 1];
                        orderingInfo += `${articles.length}. "${last.title}" (Priority: ${last.priority})\n`;
                    }
                    
                    if (isCorrectOrder) {
                        statusEl.innerHTML = '<span class="success">✅ Correct Order</span>';
                        orderingInfo += '\n✅ All articles are correctly ordered by priority (highest first)';
                    } else {
                        statusEl.innerHTML = '<span class="error">❌ Wrong Order</span>';
                        orderingInfo += '\n❌ Priority ordering is incorrect!';
                    }
                    
                    resultsEl.textContent = orderingInfo;
                } else {
                    throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                statusEl.innerHTML = '<span class="error">❌ Failed</span>';
                resultsEl.textContent = `Error: ${error.message}`;
            }
        }

        // Test Search & Filter
        async function testSearchFilter() {
            const statusEl = document.getElementById('filter-status');
            const resultsEl = document.getElementById('filter-results');
            
            statusEl.innerHTML = '<span class="warning">Testing...</span>';
            resultsEl.textContent = 'Testing search and filter functionality...';
            
            try {
                // Test category filter
                const categoryResponse = await fetch(`${API_BASE}/information?category=Peringatan Nasional`);
                const categoryData = await categoryResponse.json();
                
                // Test search
                const searchResponse = await fetch(`${API_BASE}/information?search=sumpah`);
                const searchData = await searchResponse.json();
                
                let results = 'Filter & Search Test Results:\n\n';
                results += `Category Filter (Peringatan Nasional): ${categoryData.data.length} articles\n`;
                results += `Search Filter (sumpah): ${searchData.data.length} articles\n\n`;
                
                if (categoryData.success && searchData.success) {
                    statusEl.innerHTML = '<span class="success">✅ Working</span>';
                    results += '✅ Both category filter and search are working correctly';
                } else {
                    statusEl.innerHTML = '<span class="error">❌ Failed</span>';
                    results += '❌ Filter/search functionality has issues';
                }
                
                resultsEl.textContent = results;
            } catch (error) {
                statusEl.innerHTML = '<span class="error">❌ Failed</span>';
                resultsEl.textContent = `Error: ${error.message}`;
            }
        }

        // Test Offline Storage (simplified)
        async function testOfflineStorage() {
            const statusEl = document.getElementById('storage-status');
            const resultsEl = document.getElementById('storage-results');
            
            statusEl.innerHTML = '<span class="warning">Testing...</span>';
            resultsEl.textContent = 'Testing IndexedDB storage...';
            
            try {
                // Test IndexedDB availability
                if (!window.indexedDB) {
                    throw new Error('IndexedDB not supported');
                }
                
                // Simple IndexedDB test
                const dbName = 'PPWA_TEST_DB';
                const request = indexedDB.open(dbName, 1);
                
                request.onupgradeneeded = function(event) {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('testStore')) {
                        db.createObjectStore('testStore', { keyPath: 'id' });
                    }
                };
                
                request.onsuccess = function(event) {
                    const db = event.target.result;
                    const transaction = db.transaction(['testStore'], 'readwrite');
                    const store = transaction.objectStore('testStore');
                    
                    // Test write
                    const testData = { id: 'test', data: 'offline storage test', timestamp: Date.now() };
                    store.put(testData);
                    
                    transaction.oncomplete = function() {
                        statusEl.innerHTML = '<span class="success">✅ Working</span>';
                        resultsEl.textContent = 'IndexedDB Test Results:\n✅ Database created successfully\n✅ Data write successful\n✅ Offline storage is functional';
                        db.close();
                    };
                };
                
                request.onerror = function() {
                    throw new Error('Failed to open IndexedDB');
                };
                
            } catch (error) {
                statusEl.innerHTML = '<span class="error">❌ Failed</span>';
                resultsEl.textContent = `Error: ${error.message}`;
            }
        }

        // Test Like Functionality
        async function testLikeFunctionality() {
            const statusEl = document.getElementById('like-status');
            const resultsEl = document.getElementById('like-results');
            
            statusEl.innerHTML = '<span class="warning">Testing...</span>';
            resultsEl.textContent = 'Testing like functionality...';
            
            try {
                // First get an article
                const articlesResponse = await fetch(`${API_BASE}/information?per_page=1`);
                const articlesData = await articlesResponse.json();
                
                if (!articlesData.success || articlesData.data.length === 0) {
                    throw new Error('No articles found to test like functionality');
                }
                
                const article = articlesData.data[0];
                const originalLikes = article.like_count;
                
                // Test like
                const likeResponse = await fetch(`${API_BASE}/information/${article.slug}/like`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                const likeData = await likeResponse.json();
                
                if (likeResponse.ok && likeData.success) {
                    const newLikes = likeData.data.like_count;
                    statusEl.innerHTML = '<span class="success">✅ Working</span>';
                    resultsEl.textContent = `Like Functionality Test:\n✅ Successfully liked article "${article.title}"\n✅ Like count increased from ${originalLikes} to ${newLikes}`;
                } else {
                    throw new Error(`Like API returned error: ${likeData.message || 'Unknown error'}`);
                }
                
            } catch (error) {
                statusEl.innerHTML = '<span class="error">❌ Failed</span>';
                resultsEl.textContent = `Error: ${error.message}`;
            }
        }

        // Auto-run basic connectivity test on page load
        window.addEventListener('load', function() {
            setTimeout(testApiConnectivity, 1000);
        });
    </script>
</body>
</html>
