<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Reminder;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;

class ReminderController extends Controller
{
    /**
     * Display a listing of the resource for web interface.
     */
    public function index(): View
    {
        $reminders = Reminder::byPriority()
            ->select(['id', 'title', 'description', 'reminder_date', 'reminder_time', 'status', 'priority'])
            ->paginate(15);

        return view('reminders.index', compact('reminders'));
    }

    /**
     * API endpoint to get all reminders for React PWA.
     */
    public function apiIndex(Request $request): JsonResponse
    {
        $query = Reminder::byPriority();

        // Apply filters if provided
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('user_id') && $request->user_id) {
            $query->byUser($request->user_id);
        }

        if ($request->has('upcoming') && $request->boolean('upcoming')) {
            $query->upcoming();
        }

        if ($request->has('overdue') && $request->boolean('overdue')) {
            $query->overdue();
        }

        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Pagination for API
        $perPage = min($request->get('per_page', 10), 50); // Max 50 items per page
        $reminders = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $reminders->items(),
            'pagination' => [
                'current_page' => $reminders->currentPage(),
                'last_page' => $reminders->lastPage(),
                'per_page' => $reminders->perPage(),
                'total' => $reminders->total(),
                'has_more' => $reminders->hasMorePages(),
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('reminders.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'user_id' => 'nullable|integer',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'reminder_date' => 'required|date|after_or_equal:today',
            'reminder_time' => 'required|date_format:H:i',
            'status' => ['required', Rule::in(['pending', 'completed', 'cancelled'])],
            'priority' => 'required|integer|min:1|max:5',
            'metadata' => 'nullable|array',
        ]);

        Reminder::create($validated);

        return redirect()->route('reminders.index')
            ->with('success', 'Reminder berhasil dibuat.');
    }

    /**
     * API endpoint to store a new reminder.
     */
    public function apiStore(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'nullable|integer',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'reminder_date' => 'required|date|after_or_equal:today',
            'reminder_time' => 'required|date_format:H:i',
            'status' => ['nullable', Rule::in(['pending', 'completed', 'cancelled'])],
            'priority' => 'nullable|integer|min:1|max:5',
            'metadata' => 'nullable|array',
        ]);

        // Set defaults for API
        $validated['status'] = $validated['status'] ?? 'pending';
        $validated['priority'] = $validated['priority'] ?? 3;

        $reminder = Reminder::create($validated);

        return response()->json([
            'success' => true,
            'data' => $reminder,
            'message' => 'Reminder berhasil dibuat.'
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Reminder $reminder): View
    {
        return view('reminders.show', compact('reminder'));
    }

    /**
     * API endpoint to get a specific reminder.
     */
    public function apiShow(Reminder $reminder): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $reminder
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Reminder $reminder): View
    {
        return view('reminders.edit', compact('reminder'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Reminder $reminder): RedirectResponse
    {
        $validated = $request->validate([
            'user_id' => 'nullable|integer',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'reminder_date' => 'required|date|after_or_equal:today',
            'reminder_time' => 'required|date_format:H:i',
            'status' => ['required', Rule::in(['pending', 'completed', 'cancelled'])],
            'priority' => 'required|integer|min:1|max:5',
            'metadata' => 'nullable|array',
        ]);

        $reminder->update($validated);

        return redirect()->route('reminders.index')
            ->with('success', 'Reminder berhasil diperbarui.');
    }

    /**
     * API endpoint to update a reminder.
     */
    public function apiUpdate(Request $request, Reminder $reminder): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'nullable|integer',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'reminder_date' => 'required|date|after_or_equal:today',
            'reminder_time' => 'required|date_format:H:i',
            'status' => ['required', Rule::in(['pending', 'completed', 'cancelled'])],
            'priority' => 'required|integer|min:1|max:5',
            'metadata' => 'nullable|array',
        ]);

        $reminder->update($validated);

        return response()->json([
            'success' => true,
            'data' => $reminder->fresh(),
            'message' => 'Reminder berhasil diperbarui.'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Reminder $reminder): RedirectResponse
    {
        $reminder->delete();

        return redirect()->route('reminders.index')
            ->with('success', 'Reminder berhasil dihapus.');
    }

    /**
     * API endpoint to delete a reminder.
     */
    public function apiDestroy(Reminder $reminder): JsonResponse
    {
        $reminder->delete();

        return response()->json([
            'success' => true,
            'message' => 'Reminder berhasil dihapus.'
        ]);
    }

    /**
     * API endpoint to mark reminder as completed.
     */
    public function apiMarkCompleted(Reminder $reminder): JsonResponse
    {
        $reminder->markAsCompleted();

        return response()->json([
            'success' => true,
            'data' => $reminder->fresh(),
            'message' => 'Reminder berhasil ditandai selesai.'
        ]);
    }

    /**
     * API endpoint to mark reminder as cancelled.
     */
    public function apiMarkCancelled(Reminder $reminder): JsonResponse
    {
        $reminder->markAsCancelled();

        return response()->json([
            'success' => true,
            'data' => $reminder->fresh(),
            'message' => 'Reminder berhasil dibatalkan.'
        ]);
    }

    /**
     * API endpoint to get reminder statistics.
     */
    public function apiStats(Request $request): JsonResponse
    {
        $userId = $request->get('user_id');

        $query = Reminder::query();
        if ($userId) {
            $query->byUser($userId);
        }

        $stats = [
            'total' => $query->count(),
            'pending' => $query->clone()->pending()->count(),
            'completed' => $query->clone()->completed()->count(),
            'overdue' => $query->clone()->overdue()->count(),
            'upcoming_today' => $query->clone()->where('reminder_date', now()->toDateString())->pending()->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
