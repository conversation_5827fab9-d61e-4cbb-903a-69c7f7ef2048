<?php $__env->startSection('title', 'Products - PPWA Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-box"></i> Products Management</h1>
    <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Product
    </a>
</div>

<div class="card">
    <div class="card-body">
        <?php if($products->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Brand</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($product->id); ?></td>
                            <td>
                                <?php if($product->image): ?>
                                    <img src="<?php echo e($product->image); ?>" alt="<?php echo e($product->name); ?>" 
                                         class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?php echo e($product->name); ?></strong>
                                <br>
                                <small class="text-muted"><?php echo e(Str::limit($product->description, 50)); ?></small>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?php echo e($product->category); ?></span>
                            </td>
                            <td>
                                <?php if($product->price): ?>
                                    <strong>Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></strong>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($product->merek): ?>
                                    <?php echo e($product->merek); ?>

                                    <?php if($product->unit_model): ?>
                                        <br><small class="text-muted"><?php echo e($product->unit_model); ?></small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($product->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('products.show', $product)); ?>" 
                                       class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('products.edit', $product)); ?>" 
                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('products.destroy', $product)); ?>" 
                                          method="POST" class="d-inline" 
                                          onsubmit="return confirm('Are you sure you want to delete this product?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No products found</h4>
                <p class="text-muted">Start by adding your first product.</p>
                <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add First Product
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="mt-4">
    <div class="row">
        <div class="col-md-6">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title"><i class="fas fa-info-circle"></i> API Information</h6>
                    <p class="card-text small mb-1">
                        <strong>API Endpoint:</strong> <code>GET /api/v1/products</code>
                    </p>
                    <p class="card-text small mb-0">
                        This endpoint is used by the React PWA to fetch product data.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title"><i class="fas fa-chart-bar"></i> Statistics</h6>
                    <p class="card-text small mb-1">
                        <strong>Total Products:</strong> <?php echo e($products->count()); ?>

                    </p>
                    <p class="card-text small mb-0">
                        <strong>Active Products:</strong> <?php echo e($products->where('is_active', true)->count()); ?>

                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ppwa\backend\resources\views/products/index.blade.php ENDPATH**/ ?>