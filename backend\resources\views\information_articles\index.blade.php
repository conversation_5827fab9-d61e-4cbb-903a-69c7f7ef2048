@extends('layouts.app')

@section('title', 'Manajemen Artikel Informasi')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-newspaper"></i> Manajemen Artikel Informasi
    </h1>
    <a href="{{ route('information_articles.create') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Tambah Artikel
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($articles->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Prioritas</th>
                            <th>Judul</th>
                            <th>Kategori</th>
                            <th>Status</th>
                            <th>Tanggal Publikasi</th>
                            <th>Views</th>
                            <th>Likes</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($articles as $article)
                        <tr>
                            <td>
                                <span class="badge bg-info">{{ $article->priority }}</span>
                                @if($article->is_featured)
                                    <span class="badge bg-warning ms-1">
                                        <i class="fas fa-star"></i> Featured
                                    </span>
                                @endif
                            </td>
                            <td>
                                <div class="fw-bold">{{ Str::limit($article->title, 50) }}</div>
                                @if($article->excerpt)
                                    <small class="text-muted">{{ Str::limit($article->excerpt, 80) }}</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $article->category }}</span>
                            </td>
                            <td>
                                @switch($article->status)
                                    @case('published')
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Published
                                        </span>
                                        @break
                                    @case('draft')
                                        <span class="badge bg-warning">
                                            <i class="fas fa-edit"></i> Draft
                                        </span>
                                        @break
                                    @case('archived')
                                        <span class="badge bg-dark">
                                            <i class="fas fa-archive"></i> Archived
                                        </span>
                                        @break
                                @endswitch
                            </td>
                            <td>
                                @if($article->published_at)
                                    <small>{{ $article->published_at->format('d M Y H:i') }}</small>
                                @else
                                    <small class="text-muted">-</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    <i class="fas fa-eye"></i> {{ number_format($article->view_count) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-danger">
                                    <i class="fas fa-heart"></i> {{ number_format($article->like_count) }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ route('information_articles.show', $article) }}" 
                                       class="btn btn-outline-info" title="Lihat">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('information_articles.edit', $article) }}" 
                                       class="btn btn-outline-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('information_articles.destroy', $article) }}" 
                                          method="POST" class="d-inline"
                                          onsubmit="return confirm('Apakah Anda yakin ingin menghapus artikel ini?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-outline-danger" title="Hapus">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $articles->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Belum ada artikel informasi</h5>
                <p class="text-muted">Mulai dengan membuat artikel informasi pertama Anda.</p>
                <a href="{{ route('information_articles.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Tambah Artikel Pertama
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Artikel</h6>
                        <h3 class="mb-0">{{ $articles->total() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-newspaper fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Published</h6>
                        <h3 class="mb-0">{{ $articles->where('status', 'published')->count() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Draft</h6>
                        <h3 class="mb-0">{{ $articles->where('status', 'draft')->count() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-edit fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Featured</h6>
                        <h3 class="mb-0">{{ $articles->where('is_featured', true)->count() }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
