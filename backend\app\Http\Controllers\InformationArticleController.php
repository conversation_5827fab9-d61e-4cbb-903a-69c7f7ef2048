<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\InformationArticle;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Str;

class InformationArticleController extends Controller
{
    /**
     * Display a listing of the resource for web interface.
     */
    public function index(): View
    {
        $articles = InformationArticle::byPriority()
            ->select(['id', 'title', 'excerpt', 'category', 'status', 'priority', 'published_at', 'is_featured'])
            ->paginate(15);

        return view('information_articles.index', compact('articles'));
    }

    /**
     * API endpoint to get all published articles for React PWA.
     */
    public function apiIndex(Request $request): JsonResponse
    {
        $query = InformationArticle::published()->byPriority();

        // Apply filters if provided
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        if ($request->has('featured') && $request->boolean('featured')) {
            $query->featured();
        }

        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Pagination for API
        $perPage = min($request->get('per_page', 10), 50); // Max 50 items per page
        $articles = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $articles->items(),
            'pagination' => [
                'current_page' => $articles->currentPage(),
                'last_page' => $articles->lastPage(),
                'per_page' => $articles->perPage(),
                'total' => $articles->total(),
                'has_more' => $articles->hasMorePages(),
            ]
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $categories = $this->getCategories();
        return view('information_articles.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|string|max:255',
            'image_alt_text' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'published_at' => 'nullable|date',
            'category' => 'required|string|max:100',
            'tags' => 'nullable|string',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'priority' => 'required|integer|min:0|max:999',
            'slug' => 'nullable|string|max:255|unique:information_articles,slug',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        // Process tags
        if ($validated['tags']) {
            $validated['tags'] = array_map('trim', explode(',', $validated['tags']));
        }

        // Auto-generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Set published_at to now if status is published and no date is set
        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        InformationArticle::create($validated);

        return redirect()->route('information_articles.index')
            ->with('success', 'Artikel informasi berhasil dibuat.');
    }

    /**
     * Display the specified resource.
     */
    public function show(InformationArticle $informationArticle): View
    {
        return view('information_articles.show', compact('informationArticle'));
    }

    /**
     * API endpoint to get a specific article.
     */
    public function apiShow(InformationArticle $informationArticle): JsonResponse
    {
        // Only show published articles via API
        if ($informationArticle->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'Artikel tidak ditemukan atau belum dipublikasi.'
            ], 404);
        }

        // Increment view count
        $informationArticle->incrementViewCount();

        return response()->json([
            'success' => true,
            'data' => $informationArticle
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(InformationArticle $informationArticle): View
    {
        $categories = $this->getCategories();
        return view('information_articles.edit', compact('informationArticle', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, InformationArticle $informationArticle): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|string|max:255',
            'image_alt_text' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'published_at' => 'nullable|date',
            'category' => 'required|string|max:100',
            'tags' => 'nullable|string',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'priority' => 'required|integer|min:0|max:999',
            'slug' => 'nullable|string|max:255|unique:information_articles,slug,' . $informationArticle->id,
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        // Process tags
        if ($validated['tags']) {
            $validated['tags'] = array_map('trim', explode(',', $validated['tags']));
        }

        // Auto-generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Set published_at to now if status is published and no date is set
        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $informationArticle->update($validated);

        return redirect()->route('information_articles.index')
            ->with('success', 'Artikel informasi berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(InformationArticle $informationArticle): RedirectResponse
    {
        $informationArticle->delete();

        return redirect()->route('information_articles.index')
            ->with('success', 'Artikel informasi berhasil dihapus.');
    }

    /**
     * API endpoint to get categories for filtering.
     */
    public function apiCategories(): JsonResponse
    {
        $categories = InformationArticle::published()
            ->select('category')
            ->distinct()
            ->orderBy('category')
            ->pluck('category');

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * API endpoint to increment like count.
     */
    public function apiLike(InformationArticle $informationArticle): JsonResponse
    {
        // Only allow liking published articles
        if ($informationArticle->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'Artikel tidak ditemukan atau belum dipublikasi.'
            ], 404);
        }

        $informationArticle->incrementLikeCount();

        return response()->json([
            'success' => true,
            'data' => [
                'like_count' => $informationArticle->fresh()->like_count
            ]
        ]);
    }

    /**
     * Get available categories for forms.
     */
    private function getCategories(): array
    {
        return [
            'Peringatan Nasional',
            'Promo & Diskon',
            'Prestasi & Penghargaan',
            'Tips & Panduan',
            'Berita Perusahaan',
            'Teknologi & Inovasi',
            'Keselamatan Kerja',
            'Lingkungan',
            'Kemitraan',
            'Lainnya'
        ];
    }
}
