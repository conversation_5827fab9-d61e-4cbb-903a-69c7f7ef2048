<?php $__env->startSection('title', $product->name . ' - PPWA Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-eye"></i> Product Details</h1>
    <div>
        <a href="<?php echo e(route('products.edit', $product)); ?>" class="btn btn-warning">
            <i class="fas fa-edit"></i> Edit
        </a>
        <a href="<?php echo e(route('products.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Products
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <?php if($product->image): ?>
                            <img src="<?php echo e($product->image); ?>" alt="<?php echo e($product->name); ?>" 
                                 class="img-fluid rounded shadow-sm">
                        <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-8">
                        <h2 class="mb-3"><?php echo e($product->name); ?></h2>
                        
                        <div class="mb-3">
                            <span class="badge bg-secondary fs-6"><?php echo e($product->category); ?></span>
                            <?php if($product->is_active): ?>
                                <span class="badge bg-success fs-6">Active</span>
                            <?php else: ?>
                                <span class="badge bg-danger fs-6">Inactive</span>
                            <?php endif; ?>
                        </div>

                        <p class="text-muted mb-4"><?php echo e($product->description); ?></p>

                        <?php if($product->price): ?>
                            <div class="mb-3">
                                <h4 class="text-primary mb-0">
                                    <i class="fas fa-tag"></i> Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?>

                                </h4>
                                <?php if($product->discount): ?>
                                    <small class="text-success">
                                        <i class="fas fa-percent"></i> <?php echo e($product->discount); ?>

                                    </small>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Product Specifications</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Brand/Merek:</td>
                                <td><?php echo e($product->merek ?: '-'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Unit Model:</td>
                                <td><?php echo e($product->unit_model ?: '-'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Warranty:</td>
                                <td>
                                    <?php if($product->warranty): ?>
                                        <span class="badge bg-info"><?php echo e($product->warranty); ?></span>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Product ID:</td>
                                <td><code>#<?php echo e($product->id); ?></code></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Created:</td>
                                <td><?php echo e($product->created_at->format('M d, Y H:i')); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Last Updated:</td>
                                <td><?php echo e($product->updated_at->format('M d, Y H:i')); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <?php if($product->notes): ?>
                    <div class="mt-3">
                        <h6 class="fw-bold">Notes:</h6>
                        <div class="bg-light p-3 rounded">
                            <?php echo e($product->notes); ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cogs"></i> Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('products.edit', $product)); ?>" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Product
                    </a>
                    
                    <form action="<?php echo e(route('products.destroy', $product)); ?>" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this product?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-trash"></i> Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-code"></i> API Access</h6>
            </div>
            <div class="card-body">
                <p class="small mb-2">This product can be accessed via API:</p>
                <div class="bg-light p-2 rounded">
                    <code class="small">GET /api/v1/products/<?php echo e($product->id); ?></code>
                </div>
                <p class="small mt-2 mb-0 text-muted">
                    Used by the React PWA to display product information.
                </p>
            </div>
        </div>

        <?php if($product->image): ?>
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-link"></i> Image URL</h6>
            </div>
            <div class="card-body">
                <div class="bg-light p-2 rounded">
                    <code class="small"><?php echo e($product->image); ?></code>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ppwa\backend\resources\views/products/show.blade.php ENDPATH**/ ?>