/**
 * API service untuk Reminders
 * Mengintegrasikan dengan <PERSON> backend API
 */

import type { 
  <PERSON><PERSON><PERSON>, 
  RemindersApiResponse, 
  ReminderApiResponse,
  ReminderStatsApiResponse 
} from '../types';

const API_BASE_URL = 'http://localhost:8000/api/v1';

export interface ReminderApiFilters {
  status?: 'pending' | 'completed' | 'cancelled';
  user_id?: number;
  upcoming?: boolean;
  overdue?: boolean;
  search?: string;
  per_page?: number;
  page?: number;
}

export interface CreateReminderData {
  user_id?: number;
  title: string;
  description?: string;
  reminder_date: string; // YYYY-MM-DD format
  reminder_time: string; // HH:MM format
  priority?: number; // 1-5
  status?: 'pending' | 'completed' | 'cancelled';
  metadata?: Record<string, any>;
}

export interface UpdateReminderData extends CreateReminderData {
  // Same as CreateReminderData but all fields are required for updates
}

class ReminderApiService {
  /**
   * Get all reminders with optional filters
   */
  async getReminders(filters: ReminderApiFilters = {}): Promise<RemindersApiResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.user_id) params.append('user_id', filters.user_id.toString());
      if (filters.upcoming !== undefined) params.append('upcoming', filters.upcoming.toString());
      if (filters.overdue !== undefined) params.append('overdue', filters.overdue.toString());
      if (filters.search) params.append('search', filters.search);
      if (filters.per_page) params.append('per_page', filters.per_page.toString());
      if (filters.page) params.append('page', filters.page.toString());

      const url = `${API_BASE_URL}/reminders${params.toString() ? '?' + params.toString() : ''}`;
      
      console.log('[ReminderAPI] Fetching reminders from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ReminderAPI] Reminders fetched successfully:', data);
      
      return data;
    } catch (error) {
      console.error('[ReminderAPI] Error fetching reminders:', error);
      throw error;
    }
  }

  /**
   * Get a specific reminder by ID
   */
  async getReminder(id: number): Promise<ReminderApiResponse> {
    try {
      const url = `${API_BASE_URL}/reminders/${id}`;
      
      console.log('[ReminderAPI] Fetching reminder from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ReminderAPI] Reminder fetched successfully:', data);
      
      return data;
    } catch (error) {
      console.error('[ReminderAPI] Error fetching reminder:', error);
      throw error;
    }
  }

  /**
   * Create a new reminder
   */
  async createReminder(reminderData: CreateReminderData): Promise<ReminderApiResponse> {
    try {
      const url = `${API_BASE_URL}/reminders`;
      
      console.log('[ReminderAPI] Creating reminder:', reminderData);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reminderData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ReminderAPI] Reminder created successfully:', data);
      
      return data;
    } catch (error) {
      console.error('[ReminderAPI] Error creating reminder:', error);
      throw error;
    }
  }

  /**
   * Update an existing reminder
   */
  async updateReminder(id: number, reminderData: UpdateReminderData): Promise<ReminderApiResponse> {
    try {
      const url = `${API_BASE_URL}/reminders/${id}`;
      
      console.log('[ReminderAPI] Updating reminder:', id, reminderData);
      
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reminderData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ReminderAPI] Reminder updated successfully:', data);
      
      return data;
    } catch (error) {
      console.error('[ReminderAPI] Error updating reminder:', error);
      throw error;
    }
  }

  /**
   * Delete a reminder
   */
  async deleteReminder(id: number): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${API_BASE_URL}/reminders/${id}`;
      
      console.log('[ReminderAPI] Deleting reminder:', id);
      
      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ReminderAPI] Reminder deleted successfully:', data);
      
      return data;
    } catch (error) {
      console.error('[ReminderAPI] Error deleting reminder:', error);
      throw error;
    }
  }

  /**
   * Mark reminder as completed
   */
  async markCompleted(id: number): Promise<ReminderApiResponse> {
    try {
      const url = `${API_BASE_URL}/reminders/${id}/complete`;
      
      console.log('[ReminderAPI] Marking reminder as completed:', id);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ReminderAPI] Reminder marked as completed:', data);
      
      return data;
    } catch (error) {
      console.error('[ReminderAPI] Error marking reminder as completed:', error);
      throw error;
    }
  }

  /**
   * Mark reminder as cancelled
   */
  async markCancelled(id: number): Promise<ReminderApiResponse> {
    try {
      const url = `${API_BASE_URL}/reminders/${id}/cancel`;
      
      console.log('[ReminderAPI] Marking reminder as cancelled:', id);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ReminderAPI] Reminder marked as cancelled:', data);
      
      return data;
    } catch (error) {
      console.error('[ReminderAPI] Error marking reminder as cancelled:', error);
      throw error;
    }
  }

  /**
   * Get reminder statistics
   */
  async getStats(userId?: number): Promise<ReminderStatsApiResponse> {
    try {
      const params = new URLSearchParams();
      if (userId) params.append('user_id', userId.toString());

      const url = `${API_BASE_URL}/reminders-stats${params.toString() ? '?' + params.toString() : ''}`;
      
      console.log('[ReminderAPI] Fetching stats from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[ReminderAPI] Stats fetched successfully:', data);
      
      return data;
    } catch (error) {
      console.error('[ReminderAPI] Error fetching stats:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const reminderApi = new ReminderApiService();
export default reminderApi;
