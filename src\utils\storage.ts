/**
 * IndexedDB wrapper untuk PPWA - Progressive Web App
 * Implementasi local data storage dengan sync capabilities
 */

export interface StorageItem {
  id: string;
  data: any;
  timestamp: number;
  synced: boolean;
  lastModified: number;
}

export interface UserPreferences {
  theme: string;
  language: string;
  notifications: boolean;
  autoSync: boolean;
  syncInterval: number;
}

export interface ProductData {
  id: number;
  image: string;
  title: string;
  topic: string;
  description: string;
  detailTitle: string;
  detailDescription: string;
  specifications: {
    usedTime: string;
    chargingPort: string;
    compatible: string;
    bluetooth: string;
    controlled: string;
  };
  cached: boolean;
  lastUpdated: number;
}

export interface TaskReminder {
  id: string;
  taskName: string;
  date: string;
  time: string;
  timestamp: number;
  notified: boolean;
}

class PPWAStorage {
  private dbName = 'PPWA_DB';
  private version = 1;
  private db: IDBDatabase | null = null;

  /**
   * Initialize IndexedDB database
   */
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('[Storage] Error opening database:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('[Storage] Database opened successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        console.log('[Storage] Database upgrade needed');

        // Create object stores
        this.createObjectStores(db);
      };
    });
  }

  /**
   * Create object stores for different data types
   */
  private createObjectStores(db: IDBDatabase): void {
    // Store untuk user preferences
    if (!db.objectStoreNames.contains('preferences')) {
      const preferencesStore = db.createObjectStore('preferences', { keyPath: 'id' });
      preferencesStore.createIndex('timestamp', 'timestamp', { unique: false });
    }

    // Store untuk product data
    if (!db.objectStoreNames.contains('products')) {
      const productsStore = db.createObjectStore('products', { keyPath: 'id' });
      productsStore.createIndex('cached', 'cached', { unique: false });
      productsStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
    }

    // Store untuk sync queue
    if (!db.objectStoreNames.contains('syncQueue')) {
      const syncStore = db.createObjectStore('syncQueue', { keyPath: 'id' });
      syncStore.createIndex('synced', 'synced', { unique: false });
      syncStore.createIndex('timestamp', 'timestamp', { unique: false });
    }

    // Store untuk app state
    if (!db.objectStoreNames.contains('appState')) {
      const appStateStore = db.createObjectStore('appState', { keyPath: 'key' });
      appStateStore.createIndex('lastModified', 'lastModified', { unique: false });
    }

    // Store untuk task reminders
    if (!db.objectStoreNames.contains('taskReminders')) {
      const taskStore = db.createObjectStore('taskReminders', { keyPath: 'id' });
      taskStore.createIndex('date', 'date', { unique: false });
      taskStore.createIndex('notified', 'notified', { unique: false });
      taskStore.createIndex('timestamp', 'timestamp', { unique: false });
    }

    // Store untuk information articles
    if (!db.objectStoreNames.contains('informationArticles')) {
      const articlesStore = db.createObjectStore('informationArticles', { keyPath: 'id' });
      articlesStore.createIndex('slug', 'slug', { unique: true });
      articlesStore.createIndex('category', 'category', { unique: false });
      articlesStore.createIndex('priority', 'priority', { unique: false });
      articlesStore.createIndex('published_at', 'published_at', { unique: false });
      articlesStore.createIndex('cached', 'cached', { unique: false });
      articlesStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
    }
  }

  /**
   * Save user preferences
   */
  async savePreferences(preferences: UserPreferences): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['preferences'], 'readwrite');
      const store = transaction.objectStore('preferences');

      const data = {
        id: 'user_preferences',
        ...preferences,
        timestamp: Date.now()
      };

      const request = store.put(data);

      request.onsuccess = () => {
        console.log('[Storage] Preferences saved successfully');
        resolve();
      };

      request.onerror = () => {
        console.error('[Storage] Error saving preferences:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<UserPreferences | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['preferences'], 'readonly');
      const store = transaction.objectStore('preferences');
      const request = store.get('user_preferences');

      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          const { id, timestamp, ...preferences } = result;
          resolve(preferences as UserPreferences);
        } else {
          // Return default preferences
          resolve({
            theme: 'ppwa',
            language: 'id',
            notifications: true,
            autoSync: true,
            syncInterval: 10 // minutes
          });
        }
      };

      request.onerror = () => {
        console.error('[Storage] Error getting preferences:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Save product data
   */
  async saveProducts(products: ProductData[]): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['products'], 'readwrite');
      const store = transaction.objectStore('products');

      let completed = 0;
      const total = products.length;

      if (total === 0) {
        resolve();
        return;
      }

      products.forEach((product) => {
        const productData: ProductData = {
          ...product,
          cached: true,
          lastUpdated: Date.now()
        };

        const request = store.put(productData);

        request.onsuccess = () => {
          completed++;
          if (completed === total) {
            console.log('[Storage] All products saved successfully');
            resolve();
          }
        };

        request.onerror = () => {
          console.error('[Storage] Error saving product:', product.id, request.error);
          reject(request.error);
        };
      });
    });
  }

  /**
   * Get all cached products
   */
  async getProducts(): Promise<ProductData[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['products'], 'readonly');
      const store = transaction.objectStore('products');
      const request = store.getAll();

      request.onsuccess = () => {
        console.log('[Storage] Retrieved products from cache');
        resolve(request.result || []);
      };

      request.onerror = () => {
        console.error('[Storage] Error getting products:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Add item to sync queue
   */
  async addToSyncQueue(item: Omit<StorageItem, 'id'>): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['syncQueue'], 'readwrite');
      const store = transaction.objectStore('syncQueue');

      const syncItem: StorageItem = {
        id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...item,
        timestamp: Date.now(),
        synced: false
      };

      const request = store.add(syncItem);

      request.onsuccess = () => {
        console.log('[Storage] Item added to sync queue');
        resolve();
      };

      request.onerror = () => {
        console.error('[Storage] Error adding to sync queue:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Get pending sync items
   */
  async getPendingSyncItems(): Promise<StorageItem[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['syncQueue'], 'readonly');
      const store = transaction.objectStore('syncQueue');
      const index = store.index('synced');
      const request = index.getAll(IDBKeyRange.only(false));

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        console.error('[Storage] Error getting pending sync items:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Mark sync item as completed
   */
  async markSyncCompleted(id: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['syncQueue'], 'readwrite');
      const store = transaction.objectStore('syncQueue');
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const item = getRequest.result;
        if (item) {
          item.synced = true;
          item.lastModified = Date.now();

          const putRequest = store.put(item);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(putRequest.error);
        } else {
          resolve(); // Item not found, consider it completed
        }
      };

      getRequest.onerror = () => {
        console.error('[Storage] Error marking sync completed:', getRequest.error);
        reject(getRequest.error);
      };
    });
  }

  /**
   * Save app state
   */
  async saveAppState(key: string, value: any): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['appState'], 'readwrite');
      const store = transaction.objectStore('appState');

      const data = {
        key,
        value,
        lastModified: Date.now()
      };

      const request = store.put(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get app state
   */
  async getAppState(key: string): Promise<any> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['appState'], 'readonly');
      const store = transaction.objectStore('appState');
      const request = store.get(key);

      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.value : null);
      };

      request.onerror = () => {
        console.error('[Storage] Error getting app state:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Save task reminder
   */
  async saveTaskReminder(task: TaskReminder): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['taskReminders'], 'readwrite');
      const store = transaction.objectStore('taskReminders');

      const request = store.put(task);

      request.onsuccess = () => {
        console.log('[Storage] Task reminder saved successfully');
        resolve();
      };

      request.onerror = () => {
        console.error('[Storage] Error saving task reminder:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Get all task reminders
   */
  async getTaskReminders(): Promise<TaskReminder[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['taskReminders'], 'readonly');
      const store = transaction.objectStore('taskReminders');
      const request = store.getAll();

      request.onsuccess = () => {
        console.log('[Storage] Retrieved task reminders from storage');
        resolve(request.result || []);
      };

      request.onerror = () => {
        console.error('[Storage] Error getting task reminders:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Update task reminder
   */
  async updateTaskReminder(task: TaskReminder): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['taskReminders'], 'readwrite');
      const store = transaction.objectStore('taskReminders');

      const request = store.put(task);

      request.onsuccess = () => {
        console.log('[Storage] Task reminder updated successfully');
        resolve();
      };

      request.onerror = () => {
        console.error('[Storage] Error updating task reminder:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Delete task reminder
   */
  async deleteTaskReminder(id: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['taskReminders'], 'readwrite');
      const store = transaction.objectStore('taskReminders');

      const request = store.delete(id);

      request.onsuccess = () => {
        console.log('[Storage] Task reminder deleted successfully');
        resolve();
      };

      request.onerror = () => {
        console.error('[Storage] Error deleting task reminder:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Get pending task reminders (not notified)
   */
  async getPendingTaskReminders(): Promise<TaskReminder[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['taskReminders'], 'readonly');
      const store = transaction.objectStore('taskReminders');
      const index = store.index('notified');
      const request = index.getAll(IDBKeyRange.only(false));

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        console.error('[Storage] Error getting pending task reminders:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Save information articles
   */
  async saveInformationArticles(articles: any[]): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['informationArticles'], 'readwrite');
      const store = transaction.objectStore('informationArticles');

      let completed = 0;
      const total = articles.length;

      if (total === 0) {
        resolve();
        return;
      }

      articles.forEach((article) => {
        const articleData = {
          ...article,
          cached: true,
          lastUpdated: Date.now()
        };

        const request = store.put(articleData);

        request.onsuccess = () => {
          completed++;
          if (completed === total) {
            console.log('[Storage] All information articles saved successfully');
            resolve();
          }
        };

        request.onerror = () => {
          console.error('[Storage] Error saving information article:', article.id, request.error);
          reject(request.error);
        };
      });
    });
  }

  /**
   * Get all cached information articles
   */
  async getInformationArticles(): Promise<any[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['informationArticles'], 'readonly');
      const store = transaction.objectStore('informationArticles');
      const request = store.getAll();

      request.onsuccess = () => {
        console.log('[Storage] Retrieved information articles from cache');
        resolve(request.result || []);
      };

      request.onerror = () => {
        console.error('[Storage] Error getting information articles:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Get information article by slug
   */
  async getInformationArticleBySlug(slug: string): Promise<any | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['informationArticles'], 'readonly');
      const store = transaction.objectStore('informationArticles');
      const index = store.index('slug');
      const request = index.get(slug);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        console.error('[Storage] Error getting information article by slug:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Clear all data (for reset functionality)
   */
  async clearAll(): Promise<void> {
    if (!this.db) await this.init();

    const storeNames = ['preferences', 'products', 'syncQueue', 'appState', 'taskReminders', 'informationArticles'];

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(storeNames, 'readwrite');
      let completed = 0;

      storeNames.forEach((storeName) => {
        const store = transaction.objectStore(storeName);
        const request = store.clear();

        request.onsuccess = () => {
          completed++;
          if (completed === storeNames.length) {
            console.log('[Storage] All data cleared');
            resolve();
          }
        };

        request.onerror = () => {
          console.error(`[Storage] Error clearing ${storeName}:`, request.error);
          reject(request.error);
        };
      });
    });
  }
}

// Export singleton instance
export const storage = new PPWAStorage();
