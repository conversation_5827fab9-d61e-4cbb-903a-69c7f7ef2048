/**
 * API service untuk Information Articles
 * <PERSON>ginte<PERSON><PERSON><PERSON> dengan <PERSON> backend API
 */

import type { 
  InformationArticle, 
  InformationArticlesApiResponse, 
  InformationArticleApiResponse,
  CategoriesApiResponse 
} from '../types';

const API_BASE_URL = 'http://localhost:8000/api/v1';

export interface InformationApiFilters {
  category?: string;
  featured?: boolean;
  search?: string;
  per_page?: number;
  page?: number;
}

class InformationApiService {
  /**
   * Get all published information articles
   */
  async getArticles(filters: InformationApiFilters = {}): Promise<InformationArticlesApiResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters.category) params.append('category', filters.category);
      if (filters.featured !== undefined) params.append('featured', filters.featured.toString());
      if (filters.search) params.append('search', filters.search);
      if (filters.per_page) params.append('per_page', filters.per_page.toString());
      if (filters.page) params.append('page', filters.page.toString());

      const url = `${API_BASE_URL}/information${params.toString() ? '?' + params.toString() : ''}`;
      
      console.log('[InformationAPI] Fetching articles from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: InformationArticlesApiResponse = await response.json();
      console.log('[InformationAPI] Articles fetched successfully:', data.data.length, 'articles');
      
      return data;
    } catch (error) {
      console.error('[InformationAPI] Error fetching articles:', error);
      throw error;
    }
  }

  /**
   * Get a specific article by slug
   */
  async getArticle(slug: string): Promise<InformationArticleApiResponse> {
    try {
      const url = `${API_BASE_URL}/information/${slug}`;
      
      console.log('[InformationAPI] Fetching article from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Artikel tidak ditemukan atau belum dipublikasi');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: InformationArticleApiResponse = await response.json();
      console.log('[InformationAPI] Article fetched successfully:', data.data.title);
      
      return data;
    } catch (error) {
      console.error('[InformationAPI] Error fetching article:', error);
      throw error;
    }
  }

  /**
   * Get available categories
   */
  async getCategories(): Promise<CategoriesApiResponse> {
    try {
      const url = `${API_BASE_URL}/information-categories`;
      
      console.log('[InformationAPI] Fetching categories from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: CategoriesApiResponse = await response.json();
      console.log('[InformationAPI] Categories fetched successfully:', data.data.length, 'categories');
      
      return data;
    } catch (error) {
      console.error('[InformationAPI] Error fetching categories:', error);
      throw error;
    }
  }

  /**
   * Like an article
   */
  async likeArticle(slug: string): Promise<{ success: boolean; like_count: number }> {
    try {
      const url = `${API_BASE_URL}/information/${slug}/like`;
      
      console.log('[InformationAPI] Liking article:', slug);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[InformationAPI] Article liked successfully');
      
      return {
        success: data.success,
        like_count: data.data.like_count
      };
    } catch (error) {
      console.error('[InformationAPI] Error liking article:', error);
      throw error;
    }
  }

  /**
   * Convert API article to legacy format for backward compatibility
   */
  convertToLegacyFormat(article: InformationArticle): any {
    return {
      id: article.slug,
      title: article.title,
      image: article.featured_image || '/images/icons/icon-192x192.png',
      description: article.excerpt || article.title,
      content: article.content,
      date: new Date(article.published_at).toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }),
      category: article.category
    };
  }

  /**
   * Get articles in legacy format for backward compatibility
   */
  async getArticlesLegacyFormat(filters: InformationApiFilters = {}): Promise<any[]> {
    try {
      const response = await this.getArticles(filters);
      return response.data.map(article => this.convertToLegacyFormat(article));
    } catch (error) {
      console.error('[InformationAPI] Error fetching articles in legacy format:', error);
      return [];
    }
  }

  /**
   * Check if API is available
   */
  async checkApiHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/information?per_page=1`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });
      return response.ok;
    } catch (error) {
      console.warn('[InformationAPI] API health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const informationApi = new InformationApiService();
export default informationApi;
